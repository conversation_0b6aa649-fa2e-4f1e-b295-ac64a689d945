/* OrderDetail Custom Styles - Minimalist Elegant Design */
.order-detail-container {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', '<PERSON><PERSON>s', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background: #f8fafc;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Enhanced Back Button */
.order-detail-container .elegant-back-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.order-detail-container .elegant-back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.order-detail-container .elegant-back-button:hover {
  transform: translateY(-3px) scale(1.02) !important;
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.15) !important;
  color: #3b82f6 !important;
  border-color: #bfdbfe !important;
  background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%) !important;
}

.order-detail-container .elegant-back-button:hover::before {
  left: 100%;
}

.order-detail-container .elegant-back-button:active {
  transform: translateY(-1px) scale(1.01) !important;
  transition: all 0.1s ease !important;
}

.order-detail-container .elegant-back-button .anticon {
  z-index: 2;
  position: relative;
  transition: transform 0.3s ease;
}

.order-detail-container .elegant-back-button:hover .anticon {
  transform: translateX(-2px);
}

.order-detail-container .elegant-back-button span {
  z-index: 2;
  position: relative;
}

/* Add subtle glow effect */
.order-detail-container .elegant-back-button:hover {
  box-shadow:
    0 12px 32px rgba(59, 130, 246, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* Minimalist Header */
.order-detail-container .elegant-header {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  color: #1e293b !important;
}

/* New Minimalist Order Header */
.order-detail-container .elegant-order-header {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-order-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
}

/* Minimalist Status Badge with Hover Effects */
.order-detail-container .elegant-order-header .status-badge {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-order-header .status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.3s ease;
}

.order-detail-container .elegant-order-header .status-badge:hover::before {
  left: 100%;
}

/* Minimalist Price Card */
.order-detail-container .elegant-order-header .price-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-order-header .price-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15) !important;
}

/* Minimalist Action Buttons */
.order-detail-container .elegant-order-header .ant-btn {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-order-header .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.order-detail-container .elegant-order-header .ant-btn-primary:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Responsive adjustments for new header */
@media (max-width: 768px) {
  .order-detail-container .elegant-order-header {
    padding: 24px !important;
  }

  .order-detail-container .elegant-order-header .ant-typography-h2 {
    font-size: 24px !important;
  }
}

@media (max-width: 576px) {
  .order-detail-container .elegant-order-header {
    padding: 20px !important;
  }

  .order-detail-container .elegant-order-header .ant-typography-h2 {
    font-size: 20px !important;
  }
}

/* Minimalist Order Icon */
.order-detail-container .elegant-order-icon {
  background: #f1f5f9 !important;
  border: 2px solid #e2e8f0 !important;
  color: #475569 !important;
  transition: all 0.2s ease;
}

.order-detail-container .elegant-order-icon:hover {
  background: #e2e8f0 !important;
  border-color: #cbd5e1 !important;
  transform: scale(1.02);
}

/* Minimalist Status Badge */
.order-detail-container .elegant-status-badge {
  background: #ffffff !important;
  border: 2px solid #e2e8f0 !important;
  color: #1e293b !important;
  transition: all 0.2s ease;
}

.order-detail-container .elegant-status-badge:hover {
  background: #f8fafc !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Minimalist Date and Tags */
.order-detail-container .elegant-date-badge,
.order-detail-container .elegant-payment-tag,
.order-detail-container .elegant-product-tag {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  color: #475569 !important;
  transition: all 0.2s ease;
}

.order-detail-container .elegant-date-badge:hover,
.order-detail-container .elegant-payment-tag:hover,
.order-detail-container .elegant-product-tag:hover {
  background: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Enhanced Price Card */
.order-detail-container .elegant-price-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-price-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: rotate 4s linear infinite;
  z-index: 0;
}

.order-detail-container .elegant-price-card > * {
  position: relative;
  z-index: 1;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.order-detail-container .elegant-price-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Enhanced Action Buttons */
.order-detail-container .elegant-action-button {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.order-detail-container .elegant-action-button:hover::before {
  left: 100%;
}

.order-detail-container .elegant-action-button:hover {
  transform: translateY(-3px) scale(1.05);
}

.order-detail-container .elegant-action-button.primary:hover {
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);
}

.order-detail-container .elegant-action-button.secondary:hover {
  box-shadow: 0 8px 24px rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.25) !important;
}

.order-detail-container .elegant-action-button.tertiary:hover {
  box-shadow: 0 8px 24px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.2) !important;
}

.order-detail-container .table-row-light {
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.order-detail-container .table-row-dark {
  background-color: #ffffff;
  transition: all 0.3s ease;
}

.order-detail-container .table-row-light:hover,
.order-detail-container .table-row-dark:hover {
  background-color: #e6f7ff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-detail-container .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border: none !important;
  font-family: 'Be Vietnam Pro', sans-serif !important;
  font-size: 14px !important;
  padding: 16px !important;
}

.order-detail-container .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px !important;
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .ant-descriptions-item-label {
  font-weight: 600 !important;
  color: #64748b !important;
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .ant-descriptions-item-content {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .ant-card-head-title {
  font-family: 'Be Vietnam Pro', sans-serif !important;
  font-weight: 600 !important;
}

.order-detail-container .ant-btn {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .ant-breadcrumb {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .ant-breadcrumb a {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

/* Card hover effects */
.order-detail-container .order-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.order-detail-container .order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.order-detail-container .order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.order-detail-container .order-card:hover::before {
  left: 100%;
}

/* Button animations */
.order-detail-container .ant-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.order-detail-container .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.order-detail-container .ant-btn:active {
  transform: translateY(0);
}

/* Status tag animations */
.order-detail-container .ant-tag {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.order-detail-container .ant-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Progress bar styling */
.order-detail-container .ant-progress-bg {
  border-radius: 4px !important;
}

.order-detail-container .ant-progress-outer {
  border-radius: 4px !important;
}

/* Modal styling */
.order-detail-container .ant-modal-content {
  border-radius: 16px !important;
  overflow: hidden;
}

.order-detail-container .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid #e2e8f0 !important;
  padding: 20px 24px !important;
}

.order-detail-container .ant-modal-body {
  padding: 0 24px !important;
}

.order-detail-container .ant-modal-footer {
  background: #f8fafc !important;
  border-top: 1px solid #e2e8f0 !important;
  padding: 16px 24px !important;
}

/* Select dropdown styling */
.order-detail-container .ant-select-dropdown {
  border-radius: 12px !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
}

.order-detail-container .ant-select-item {
  border-radius: 8px !important;
  margin: 4px 8px !important;
  transition: all 0.2s ease !important;
}

.order-detail-container .ant-select-item:hover {
  background: #f1f5f9 !important;
  transform: translateX(4px);
}

/* Image styling */
.order-detail-container .ant-image {
  border-radius: 8px !important;
  overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .order-detail-container {
    padding: 16px !important;
  }
  
  .order-detail-container .ant-card {
    margin-bottom: 16px !important;
  }
  
  .order-detail-container .ant-col {
    margin-bottom: 16px;
  }
}

/* Loading spinner styling */
.order-detail-container .ant-spin-text {
  font-family: 'Be Vietnam Pro', sans-serif !important;
  color: #64748b !important;
  margin-top: 12px !important;
}

/* Empty state styling */
.order-detail-container .ant-empty-description {
  font-family: 'Be Vietnam Pro', sans-serif !important;
  color: #64748b !important;
}

/* Divider styling */
.order-detail-container .ant-divider {
  border-color: #e2e8f0 !important;
}

/* Table scroll styling */
.order-detail-container .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.order-detail-container .ant-table-body::-webkit-scrollbar {
  height: 6px;
}

.order-detail-container .ant-table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.order-detail-container .ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.order-detail-container .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Tooltip styling */
.order-detail-container .ant-tooltip-inner {
  background: #1e293b !important;
  border-radius: 8px !important;
  font-family: 'Be Vietnam Pro', sans-serif !important;
  font-size: 13px !important;
  padding: 8px 12px !important;
}

.order-detail-container .ant-tooltip-arrow::before {
  background: #1e293b !important;
}

/* Spin loading styling */
.order-detail-container .ant-spin-dot-item {
  background-color: #667eea !important;
}

.order-detail-container .ant-spin-text {
  color: #667eea !important;
  font-weight: 500 !important;
}

/* Image styling improvements */
.order-detail-container .ant-image-img {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.order-detail-container .ant-image:hover .ant-image-img {
  transform: scale(1.05);
}

/* Typography improvements */
.order-detail-container h1,
.order-detail-container h2,
.order-detail-container h3,
.order-detail-container h4,
.order-detail-container h5,
.order-detail-container h6 {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

/* Enhanced Progress Container */
.order-detail-container .elegant-progress-container {
  position: relative;
  overflow: hidden;
}

.order-detail-container .elegant-progress-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  animation: rotate 6s linear infinite;
  z-index: 0;
}

.order-detail-container .elegant-progress-container > * {
  position: relative;
  z-index: 1;
}

/* Minimalist Order Progress Steps styling */
.order-detail-container .elegant-steps,
.order-detail-container .order-progress-steps {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .elegant-steps .ant-steps-item-icon,
.order-detail-container .order-progress-steps .ant-steps-item-icon {
  width: 32px !important;
  height: 32px !important;
  line-height: 30px !important;
  font-size: 14px !important;
  border-width: 2px !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-finish .ant-steps-item-icon {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: white !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white !important;
}

/* Enhanced Active Step Styling with Special Effects - FORCE OVERRIDE */
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-icon,
.order-detail-container .elegant-steps .ant-steps-item-process .ant-steps-item-icon,
.order-progress-steps .ant-steps-item-process .ant-steps-item-icon {
  background-color: #ff0000 !important;
  border-color: #ff0000 !important;
  color: white !important;
  position: relative !important;
  transform: scale(1.5) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 10 !important;
  overflow: visible !important;
  animation: testPulse 1s ease-in-out infinite !important;
}

/* Rotating outer ring effect for active step */
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-icon::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, currentColor, transparent, currentColor, transparent);
  animation: rotate 3s linear infinite;
  z-index: -1;
  opacity: 0.7;
}

/* Pulsing inner glow effect */
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-icon::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: radial-gradient(circle, currentColor 0%, transparent 70%);
  animation: pulse 2s ease-in-out infinite;
  z-index: -1;
  opacity: 0.5;
}

/* Status-specific colors and effects with enhanced glow */
.order-detail-container .order-progress-steps[data-current-status="Chờ xác nhận"] .ant-steps-item-process .ant-steps-item-icon {
  background-color: #f59e0b !important;
  border-color: #f59e0b !important;
  color: white !important;
  animation: pulseOrange 2s ease-in-out infinite, bounce 3s ease-in-out infinite;
  box-shadow:
    0 0 20px rgba(245, 158, 11, 0.6),
    0 0 40px rgba(245, 158, 11, 0.4),
    0 0 60px rgba(245, 158, 11, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Chờ giao hàng"] .ant-steps-item-process .ant-steps-item-icon {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: white !important;
  animation: pulseBlue 2s ease-in-out infinite, bounce 3s ease-in-out infinite;
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.6),
    0 0 40px rgba(59, 130, 246, 0.4),
    0 0 60px rgba(59, 130, 246, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Đang giao hàng"] .ant-steps-item-process .ant-steps-item-icon {
  background-color: #8b5cf6 !important;
  border-color: #8b5cf6 !important;
  color: white !important;
  animation: pulsePurple 2s ease-in-out infinite, bounce 3s ease-in-out infinite;
  box-shadow:
    0 0 20px rgba(139, 92, 246, 0.6),
    0 0 40px rgba(139, 92, 246, 0.4),
    0 0 60px rgba(139, 92, 246, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Đã hoàn thành"] .ant-steps-item-process .ant-steps-item-icon {
  background-color: #10b981 !important;
  border-color: #10b981 !important;
  color: white !important;
  animation: pulseGreen 2s ease-in-out infinite, bounce 3s ease-in-out infinite;
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.6),
    0 0 40px rgba(16, 185, 129, 0.4),
    0 0 60px rgba(16, 185, 129, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-icon .ant-steps-icon {
  color: white !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-wait .ant-steps-item-icon {
  background-color: #f1f5f9 !important;
  border-color: #e2e8f0 !important;
  color: #64748b !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-wait .ant-steps-item-icon .ant-steps-icon {
  color: #64748b !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-error .ant-steps-item-icon {
  background-color: #ef4444 !important;
  border-color: #ef4444 !important;
  color: white !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-error .ant-steps-item-icon .ant-steps-icon {
  color: white !important;
}

/* Text glow animation for active step titles */
@keyframes textGlow {
  0% {
    text-shadow: 0 0 5px currentColor;
  }
  50% {
    text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
  100% {
    text-shadow: 0 0 5px currentColor;
  }
}

/* Pulse animation for step containers */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px transparent;
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 transparent;
  }
}

/* Spin animation for rotating ring */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Pulse animation for current step */
@keyframes pulse {
  0% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1.2);
  }
}

/* Bounce animation for active step */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1.2);
  }
  40% {
    transform: translateY(-3px) scale(1.22);
  }
  60% {
    transform: translateY(-2px) scale(1.21);
  }
}

/* Specific pulse animations for different states */
@keyframes pulseOrange {
  0% {
    box-shadow: 0 0 0 4px rgba(250, 173, 20, 0.3), 0 4px 12px rgba(250, 173, 20, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(250, 173, 20, 0.2), 0 4px 12px rgba(250, 173, 20, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(250, 173, 20, 0.3), 0 4px 12px rgba(250, 173, 20, 0.4);
  }
}

@keyframes pulseBlue {
  0% {
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.3), 0 4px 12px rgba(24, 144, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.2), 0 4px 12px rgba(24, 144, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.3), 0 4px 12px rgba(24, 144, 255, 0.4);
  }
}

@keyframes pulsePurple {
  0% {
    box-shadow: 0 0 0 4px rgba(114, 46, 209, 0.3), 0 4px 12px rgba(114, 46, 209, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(114, 46, 209, 0.2), 0 4px 12px rgba(114, 46, 209, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(114, 46, 209, 0.3), 0 4px 12px rgba(114, 46, 209, 0.4);
  }
}

@keyframes pulseGreen {
  0% {
    box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.3), 0 4px 12px rgba(82, 196, 26, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(82, 196, 26, 0.2), 0 4px 12px rgba(82, 196, 26, 0.4);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.3), 0 4px 12px rgba(82, 196, 26, 0.4);
  }
}

/* Enhanced Step Connection Lines */
.order-detail-container .order-progress-steps .ant-steps-item-finish .ant-steps-item-tail::after {
  background-color: #10b981 !important;
  height: 3px !important;
  top: 50% !important;
  transform: translateY(-50%);
  border-radius: 2px;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
}

/* Active step connection line with loading effect */
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-tail::after,
.order-detail-container .elegant-steps .ant-steps-item-process .ant-steps-item-tail::after {
  height: 4px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-radius: 2px !important;
  background: #e2e8f0 !important;
  position: relative !important;
  overflow: hidden !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-tail::before,
.order-detail-container .elegant-steps .ant-steps-item-process .ant-steps-item-tail::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg,
    transparent 0%,
    #3b82f6 20%,
    #60a5fa 50%,
    #3b82f6 80%,
    transparent 100%
  ) !important;
  animation: loadingLine 2s ease-in-out infinite !important;
  border-radius: 2px !important;
  box-shadow: 0 0 8px #3b82f6 !important;
  z-index: 1 !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-wait .ant-steps-item-tail::after {
  background-color: #e2e8f0 !important;
  height: 2px !important;
  top: 50% !important;
  transform: translateY(-50%);
  border-radius: 1px;
}

/* Loading line animation - moves from left to right continuously */
@keyframes loadingLine {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* Status-specific loading line colors for active step */
.order-detail-container .order-progress-steps[data-current-status="Chờ xác nhận"] .ant-steps-item-process .ant-steps-item-tail::before,
.order-detail-container .elegant-steps[data-current-status="Chờ xác nhận"] .ant-steps-item-process .ant-steps-item-tail::before {
  background: linear-gradient(90deg,
    transparent 0%,
    #f59e0b 20%,
    #fbbf24 50%,
    #f59e0b 80%,
    transparent 100%
  ) !important;
  box-shadow: 0 0 12px #f59e0b, 0 0 24px rgba(245, 158, 11, 0.5) !important;
}

.order-detail-container .order-progress-steps[data-current-status="Chờ giao hàng"] .ant-steps-item-process .ant-steps-item-tail::before,
.order-detail-container .elegant-steps[data-current-status="Chờ giao hàng"] .ant-steps-item-process .ant-steps-item-tail::before {
  background: linear-gradient(90deg,
    transparent 0%,
    #3b82f6 20%,
    #60a5fa 50%,
    #3b82f6 80%,
    transparent 100%
  ) !important;
  box-shadow: 0 0 12px #3b82f6, 0 0 24px rgba(59, 130, 246, 0.5) !important;
}

.order-detail-container .order-progress-steps[data-current-status="Đang giao hàng"] .ant-steps-item-process .ant-steps-item-tail::before,
.order-detail-container .elegant-steps[data-current-status="Đang giao hàng"] .ant-steps-item-process .ant-steps-item-tail::before {
  background: linear-gradient(90deg,
    transparent 0%,
    #8b5cf6 20%,
    #a78bfa 50%,
    #8b5cf6 80%,
    transparent 100%
  ) !important;
  box-shadow: 0 0 12px #8b5cf6, 0 0 24px rgba(139, 92, 246, 0.5) !important;
}

.order-detail-container .order-progress-steps[data-current-status="Đã hoàn thành"] .ant-steps-item-process .ant-steps-item-tail::before,
.order-detail-container .elegant-steps[data-current-status="Đã hoàn thành"] .ant-steps-item-process .ant-steps-item-tail::before {
  background: linear-gradient(90deg,
    transparent 0%,
    #10b981 20%,
    #34d399 50%,
    #10b981 80%,
    transparent 100%
  ) !important;
  box-shadow: 0 0 12px #10b981, 0 0 24px rgba(16, 185, 129, 0.5) !important;
}

/* Enhanced Active Step Text Styling */
.order-detail-container .order-progress-steps .ant-steps-item .ant-steps-item-title {
  color: #1e293b !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.order-detail-container .order-progress-steps .ant-steps-item .ant-steps-item-description {
  color: #64748b !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-content {
  margin-top: 8px !important;
}

/* Active step text effects */
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-title,
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-title span {
  color: #1e293b !important;
  font-weight: 700 !important;
  font-size: 16px !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  animation: textGlow 2s ease-in-out infinite alternate, textBounce 3s ease-in-out infinite !important;
  position: relative !important;
  z-index: 2 !important;
}

.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-description,
.order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-description span {
  color: #475569 !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  animation: fadeInOut 3s ease-in-out infinite !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Text glow animation */
@keyframes textGlow {
  0% {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  100% {
    text-shadow:
      0 2px 8px rgba(0, 0, 0, 0.2),
      0 0 12px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2);
  }
}

/* Text bounce animation */
@keyframes textBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1px);
  }
}

/* Fade in/out animation for description */
@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Status-specific text colors for active step */
.order-detail-container .order-progress-steps[data-current-status="Chờ xác nhận"] .ant-steps-item-process .ant-steps-item-title {
  color: #92400e !important;
}

.order-detail-container .order-progress-steps[data-current-status="Chờ giao hàng"] .ant-steps-item-process .ant-steps-item-title {
  color: #1e40af !important;
}

.order-detail-container .order-progress-steps[data-current-status="Đang giao hàng"] .ant-steps-item-process .ant-steps-item-title {
  color: #6b21a8 !important;
}

.order-detail-container .order-progress-steps[data-current-status="Đã hoàn thành"] .ant-steps-item-process .ant-steps-item-title {
  color: #065f46 !important;
}

/* Enhanced Step Container Hover Effects */
.order-detail-container .order-progress-steps .ant-steps-item {
  transition: all 0.3s ease !important;
  border-radius: 12px;
  padding: 8px !important;
  margin: 0 4px;
}

.order-detail-container .order-progress-steps .ant-steps-item:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Special hover effect for active step */
.order-detail-container .order-progress-steps .ant-steps-item-process:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Breathing effect for active step container */
.order-detail-container .order-progress-steps .ant-steps-item-process {
  animation: breathe 3s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

/* Shimmer effect for active step */
.order-detail-container .order-progress-steps .ant-steps-item-process::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  animation: shimmer 4s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Status-specific container glow with enhanced effects */
.order-detail-container .order-progress-steps[data-current-status="Chờ xác nhận"] .ant-steps-item-process {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08), rgba(245, 158, 11, 0.03));
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 0 0 1px rgba(245, 158, 11, 0.3),
    0 4px 20px rgba(245, 158, 11, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Chờ giao hàng"] .ant-steps-item-process {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.03));
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 0 0 1px rgba(59, 130, 246, 0.3),
    0 4px 20px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Đang giao hàng"] .ant-steps-item-process {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.08), rgba(139, 92, 246, 0.03));
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 0 0 1px rgba(139, 92, 246, 0.3),
    0 4px 20px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.order-detail-container .order-progress-steps[data-current-status="Đã hoàn thành"] .ant-steps-item-process {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08), rgba(16, 185, 129, 0.03));
  border: 2px solid transparent;
  background-clip: padding-box;
  box-shadow:
    0 0 0 1px rgba(16, 185, 129, 0.3),
    0 4px 20px rgba(16, 185, 129, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Responsive steps */
@media (max-width: 768px) {
  .order-detail-container .order-progress-steps {
    direction: ltr;
  }

  .order-detail-container .order-progress-steps .ant-steps-item {
    padding: 6px !important;
    margin: 0 2px;
  }

  .order-detail-container .order-progress-steps .ant-steps-item-title {
    font-size: 13px !important;
  }

  .order-detail-container .order-progress-steps .ant-steps-item-description {
    font-size: 11px !important;
  }

  /* Reduce animations on mobile for performance */
  .order-detail-container .order-progress-steps .ant-steps-item-process {
    animation: none;
  }

  .order-detail-container .order-progress-steps .ant-steps-item-process .ant-steps-item-icon {
    animation: none;
    transform: scale(1.1) !important;
  }
}

/* Minimalist Tabs Container */
.order-detail-container .elegant-tabs-container {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Minimalist Tabs styling */
.order-detail-container .elegant-tabs {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-detail-container .elegant-tabs .ant-tabs-tab {
  border-radius: 8px 8px 0 0 !important;
  margin-right: 8px !important;
  padding: 12px 24px !important;
  transition: all 0.2s ease !important;
  border: 1px solid #e2e8f0 !important;
  background: #f8fafc !important;
  font-weight: 600 !important;
  color: #64748b !important;
}

.order-detail-container .elegant-tabs .ant-tabs-tab:hover {
  background: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  color: #475569 !important;
}

.order-detail-container .elegant-tabs .ant-tabs-tab-active {
  background: #ffffff !important;
  color: #1e293b !important;
  border-color: #e2e8f0 !important;
  border-bottom-color: #ffffff !important;
  position: relative;
  z-index: 1;
}

.order-detail-container .elegant-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1e293b !important;
  font-weight: 700 !important;
}

.order-detail-container .elegant-tabs .ant-tabs-content-holder {
  background: transparent !important;
  border-radius: 0 32px 32px 32px !important;
  overflow: hidden;
  margin-top: 8px;
}

.order-detail-container .elegant-tabs .ant-tabs-content {
  padding: 0 !important;
}

.order-detail-container .elegant-tabs .ant-tabs-tabpane {
  padding: 0 !important;
}

.order-detail-container .elegant-tabs .ant-tabs-ink-bar {
  display: none !important;
}

.order-detail-container .elegant-tabs .ant-tabs-nav {
  margin-bottom: 0 !important;
}

.order-detail-container .elegant-tabs .ant-tabs-nav-wrap {
  padding: 0 !important;
}

/* Minimalist card backgrounds */
.order-detail-container .customer-card {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.order-detail-container .customer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #6366f1;
  z-index: 1;
}

.order-detail-container .order-info-card {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.order-detail-container .order-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #0ea5e9;
  z-index: 1;
}

.order-detail-container .financial-card {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.order-detail-container .financial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #10b981;
  z-index: 1;
}

.order-detail-container .products-card {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.order-detail-container .products-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #f59e0b;
  z-index: 1;
}

.order-detail-container .commission-card {
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.order-detail-container .commission-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  z-index: 1;
}

/* Animation for status change */
@keyframes statusPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.order-detail-container .status-updating {
  animation: statusPulse 1s infinite;
}

/* Enhanced Card Content Styling */
.order-detail-container .ant-card-body {
  position: relative;
  z-index: 2;
}

/* Enhanced Modal Styling */
.order-detail-container .ant-modal-content {
  border-radius: 24px !important;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15) !important;
}

.order-detail-container .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5) !important;
  padding: 24px 32px !important;
}

.order-detail-container .ant-modal-body {
  padding: 0 32px !important;
  background: rgba(255, 255, 255, 0.98);
}

.order-detail-container .ant-modal-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border-top: 1px solid rgba(226, 232, 240, 0.5) !important;
  padding: 20px 32px !important;
}

/* Enhanced Responsive improvements */
@media (max-width: 1200px) {
  .order-detail-container {
    padding: 24px !important;
  }

  .order-detail-container .elegant-header {
    padding: 40px !important;
  }
}

@media (max-width: 992px) {
  .order-detail-container {
    padding: 20px !important;
  }

  .order-detail-container .elegant-header {
    padding: 32px !important;
  }

  .order-detail-container .ant-card {
    margin-bottom: 24px !important;
  }

  .order-detail-container .elegant-tabs-container {
    padding: 24px !important;
  }
}

@media (max-width: 768px) {
  .order-detail-container {
    padding: 16px !important;
  }

  .order-detail-container .elegant-header {
    padding: 24px !important;
    border-radius: 24px !important;
  }

  .order-detail-container .elegant-order-icon {
    width: 72px !important;
    height: 72px !important;
    font-size: 32px !important;
  }

  .order-detail-container .elegant-tabs-container {
    padding: 20px !important;
    border-radius: 24px !important;
  }
}

@media (max-width: 576px) {
  .order-detail-container {
    padding: 12px !important;
  }

  .order-detail-container .elegant-header {
    padding: 20px !important;
    border-radius: 20px !important;
  }

  .order-detail-container .ant-modal {
    margin: 0 !important;
    max-width: 100% !important;
  }

  .order-detail-container .ant-modal-content {
    border-radius: 16px !important;
  }

  .order-detail-container .elegant-tabs .ant-tabs-tab {
    padding: 12px 16px !important;
    margin-right: 8px !important;
  }
}

/* Simple hover effects */
.order-detail-container .order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced Loading States */
.order-detail-container .skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Advanced Typography Enhancements */
.order-detail-container .elegant-typography {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Sophisticated Color Transitions */
.order-detail-container .color-transition {
  transition:
    color 0.3s ease,
    background-color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* Enhanced Focus States for Accessibility */
.order-detail-container .accessible-focus:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(99, 102, 241, 0.3),
    0 0 0 1px rgba(99, 102, 241, 0.5);
}

/* Advanced Scroll Enhancements */
.order-detail-container .smooth-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.3) transparent;
}

.order-detail-container .smooth-scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.order-detail-container .smooth-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.order-detail-container .smooth-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.order-detail-container .smooth-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}
