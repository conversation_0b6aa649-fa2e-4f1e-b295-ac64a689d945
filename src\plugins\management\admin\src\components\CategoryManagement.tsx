import React, { useState, useEffect } from 'react';
import { Table, Button, message, Switch } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  CategoryBrandModal,
} from './shared';

interface Category {
  id: number;
  documentId: string;
  name: string;
  image?: {
    id: number;
    url: string;
    name: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const CategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [searchText, setSearchText] = useState('');
  const [deleting, setDeleting] = useState<number | null>(null);
  const { get, del } = useFetchClient();

  // Fetch categories
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await get('/management/products/categories');
      console.log(response);
      setCategories(response.data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      message.error('Không thể tải danh sách danh mục');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle modal success
  const handleModalSuccess = () => {
    setModalVisible(false);
    setEditingCategory(null);
    fetchCategories();
  };

  // Handle delete category
  const handleDelete = async (id: number) => {
    if (deleting === id) return; // Prevent double deletion

    setDeleting(id);
    try {
      await del(`/management/products/categories/${id}`);
      message.success('Xóa danh mục thành công');
      fetchCategories();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa danh mục');
    } finally {
      setDeleting(null);
    }
  };

  // Handle edit
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setModalVisible(true);
  };

  // Handle add new
  const handleAdd = () => {
    setEditingCategory(null);
    setModalVisible(true);
  };

  // Filter categories based on search
  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Hình ảnh',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      render: (image: any) => (
        <ImageDisplay
          src={image?.url}
          alt="Category"
          size={50}
          placeholder="Không có"
          preview={true}
        />
      ),
    },
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Category, b: Category) => a.name.localeCompare(b.name),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 120,
      render: (isActive: boolean) => (
        <Switch
          checked={isActive}
          disabled
          size="small"
          checkedChildren="Hoạt động"
          unCheckedChildren="Tạm dừng"
        />
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a: Category, b: Category) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_: any, record: Category) => (
        <ActionButtonGroup
          onEdit={() => handleEdit(record)}
          onDelete={() => handleDelete(record.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa danh mục này?"
          showView={false}
          showEdit={true}
          showDelete={true}
          editTooltip="Chỉnh sửa danh mục"
          deleteTooltip="Xóa danh mục"
          deleteLoading={deleting === record.id}
          disabled={deleting !== null}
        />
      ),
    },
  ];

  return (
    <PageContainer style={{ background: '#f8fafc', minHeight: '100vh' }}>
      <Card>
        <PageHeader
          title="Quản lý danh mục sản phẩm"
          description="Xem và quản lý danh mục sản phẩm"
          actions={
            <div style={{ display: 'flex', gap: 8 }}>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} size="large">
                Thêm danh mục
              </Button>
            </div>
          }
        />

        <CardContent>
          <FiltersSection>
            <SearchBar
              placeholder="Tìm kiếm danh mục..."
              value={searchText}
              onChange={setSearchText}
            />
          </FiltersSection>

          <StyledTable>
            <Table
              columns={columns}
              dataSource={filteredCategories}
              rowKey="id"
              loading={loading}
              pagination={{
                total: filteredCategories.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} danh mục`,
              }}
              scroll={{ x: 800 }}
            />
          </StyledTable>
        </CardContent>
      </Card>

      <CategoryBrandModal
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCategory(null);
        }}
        type="category"
        editingItem={editingCategory}
        onSuccess={handleModalSuccess}
        showActiveSwitch={true}
      />
    </PageContainer>
  );
};

export default CategoryManagement;
