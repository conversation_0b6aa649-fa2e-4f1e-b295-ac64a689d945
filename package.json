{"name": "liberty-backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"install-lib": "cd src/plugins/management && npm install && cd ../../.. && yarn install", "build": "cd src/plugins/management && npm run build && cd ../../.. && cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "watch-admin": "npm rebuild sharp && yarn develop --watch-admin"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fontsource/be-vietnam-pro": "^5.0.8", "@strapi/plugin-cloud": "5.17.0", "@strapi/plugin-color-picker": "^5.17.0", "@strapi/plugin-documentation": "^5.17.0", "@strapi/plugin-users-permissions": "5.17.0", "@strapi/strapi": "5.17.0", "antd": "^5.26.4", "exceljs": "^4.4.0", "pg": "^8.16.3", "react": "^18.0.0", "react-dom": "^18.0.0", "react-intl": "^6.6.2", "react-router-dom": "^6.0.0", "recharts": "^3.0.2", "strapi-plugin-multi-select": "^2.1.1", "styled-components": "^6.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "48b3e29f09fb7aeab8d261a8b1b425db3da88e84fa186dcf73949156f60e3f6f"}}