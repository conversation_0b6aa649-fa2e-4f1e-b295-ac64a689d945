import type { Core } from '@strapi/strapi';

const withdrawalController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listWithdrawals(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .listWithdrawals(ctx.query);
  },

  async getWithdrawalDetail(ctx) {
    const { id } = ctx.params;
    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .getWithdrawalDetail(Number(id));
  },

  async approveWithdrawal(ctx) {
    try {
      const { id } = ctx.params;
      const adminUser = ctx.state.user;

      const result = await strapi
        .plugin('management')
        .service('withdrawal-service')
        .approveWithdrawal(Number(id), adminUser);

      ctx.body = {
        success: true,
        data: result,
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  async rejectWithdrawal(ctx) {
    try {
      const { id } = ctx.params;
      const { adminNote } = ctx.request.body;
      const adminUser = ctx.state.user;

      const result = await strapi
        .plugin('management')
        .service('withdrawal-service')
        .rejectWithdrawal(Number(id), adminNote, adminUser);

      ctx.body = {
        success: true,
        data: result,
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  async getWithdrawalStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('withdrawal-service')
      .getWithdrawalStatistics(ctx.query);
  },
});

export default withdrawalController;
