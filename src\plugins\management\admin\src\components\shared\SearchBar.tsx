import React from 'react';
import { Search as SearchIcon } from 'lucide-react';
import { SearchContainer, SearchInput } from './StyledComponents';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Tìm kiếm...',
  value,
  onChange,
  className,
}) => {
  return (
    <SearchContainer className={className}>
      <SearchIcon />
      <SearchInput
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </SearchContainer>
  );
};

export default SearchBar;
