// @ts-ignore
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import 'dayjs/locale/vi';
import * as XLSX from 'xlsx';
// @ts-ignore
import {
  Table,
  Button,
  Input,
  Pagination,
  Spin,
  Space,
  message,
  Typography,
  Card,
  Tag,
  Row,
  Col,
  Badge,
  Tooltip,
  Empty,
  Tabs,
  Modal,
  Radio,
  DatePicker,
  Checkbox,
} from 'antd';
// @ts-ignore
import {
  FileExcelOutlined,
  ShoppingCartOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  ExclamationCircleOutlined,
  SendOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import 'antd/dist/reset.css';
import './OrderList.css';
import { useFetchClient } from '@strapi/strapi/admin';

const { Title, Paragraph } = Typography;
const { RangePicker } = DatePicker;

// Set dayjs locale to Vietnamese
dayjs.locale('vi');

const statusOptions = [
  { value: '', label: 'Tất cả', color: '#666', icon: null },
  { value: 'Chờ xác nhận', label: 'Chờ xác nhận', color: '#faad14', icon: <ClockCircleOutlined /> },
  { value: 'Chờ giao hàng', label: 'Chờ giao hàng', color: '#722ed1', icon: <SendOutlined /> },
  { value: 'Đang giao hàng', label: 'Đang giao hàng', color: '#1890ff', icon: <TruckOutlined /> },
  {
    value: 'Đã hoàn thành',
    label: 'Đã hoàn thành',
    color: '#52c41a',
    icon: <CheckCircleOutlined />,
  },
  { value: 'Đã hủy', label: 'Đã hủy', color: '#ff4d4f', icon: <ExclamationCircleOutlined /> },
];

// Define valid status transitions
const statusTransitions: Record<string, string[]> = {
  'Chờ xác nhận': ['Chờ giao hàng', 'Đã hủy'],
  'Chờ giao hàng': ['Đang giao hàng', 'Đã hủy'],
  'Đang giao hàng': ['Đã hoàn thành', 'Đã hủy'],
  'Đã hoàn thành': [], // Cannot change from completed
  'Đã hủy': [], // Cannot change from cancelled
};

// Get available status options for transition
const getAvailableStatusOptions = (currentStatus: string) => {
  const availableStatuses = statusTransitions[currentStatus] || [];
  return statusOptions.filter((option) => availableStatuses.includes(option.value));
};

// Tab items for status filtering
const getTabItems = (orders: any[]) => {
  const getCountByStatus = (status: string) => {
    if (status === '') return orders.length;
    return orders.filter((order) => order.statusOrder === status).length;
  };

  // Statuses that should show badge counts
  const statusesWithBadge = ['Chờ xác nhận', 'Chờ giao hàng', 'Đang giao hàng'];

  return statusOptions.map((option) => ({
    key: option.value,
    label: (
      <div style={{ display: 'flex', alignItems: 'center', gap: 0 }}>
        {option.icon}
        <span style={{ marginRight: 6 }}>{option.label}</span>
        {statusesWithBadge.includes(option.value) && (
          <Badge
            count={getCountByStatus(option.value)}
            style={{
              backgroundColor: option.value === 'Chờ xác nhận' ? '#ff4d4f' : option.color,
              fontSize: '12px',
              width: '20px',
              height: '20px',
            }}
          />
        )}
      </div>
    ),
  }));
};

const PAGE_SIZE = 10;

// Helper function to get status config
const getStatusConfig = (status: string) => {
  return statusOptions.find((opt) => opt.value === status) || statusOptions[0];
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

// Function to create and download Excel file
const createAndDownloadExcel = async (orders: any[]) => {
  // Create workbook
  const workbook = XLSX.utils.book_new();

  // Prepare data for single sheet with products as comma-separated values
  const excelData = orders.map((order, index) => {
    const customer = order.customer || {};
    const products = order.products || [];

    // Prepare product information as comma-separated strings
    let productNames = 'Không có sản phẩm';
    let productQuantities = 'N/A';
    let productPrices = 'N/A';
    let productTotals = 'N/A';

    if (products.length > 0) {
      productNames = products.map((p: any) => p.name || 'N/A').join(', ');
      productQuantities = products.map((p: any) => p.quantity || 0).join(', ');
      productPrices = products.map((p: any) => (p.price || 0).toLocaleString('vi-VN')).join(', ');
      productTotals = products
        .map((p: any) => ((p.quantity || 0) * (p.price || 0)).toLocaleString('vi-VN'))
        .join(', ');
    }

    return {
      STT: index + 1,
      ID: order.id,
      'Mã đơn hàng': order.code,
      'Trạng thái': order.statusOrder,
      'Tên khách hàng': customer.name || 'N/A',
      'Số điện thoại': customer.phone || 'N/A',
      Email: customer.email || 'N/A',
      'Địa chỉ': customer.address
        ? `${customer.address}, ${customer.ward || ''}, ${customer.district || ''}, ${customer.city || ''}`
            .replace(/,\s*,/g, ',')
            .replace(/^,|,$/g, '')
        : 'N/A',
      'Danh sách sản phẩm': productNames,
      'Số lượng': productQuantities,
      'Đơn giá': productPrices,
      'Thành tiền sản phẩm': productTotals,
      'Tổng tiền trước thuế': order.priceBeforeTax || 0,
      'Thuế VAT': order.taxAmount || 0,
      'Tổng tiền sau thuế': order.priceAfterTax || 0,
      'Ngày tạo': order.createdAt ? dayjs(order.createdAt).format('DD/MM/YYYY HH:mm:ss') : 'N/A',
      'Ngày cập nhật': order.updatedAt
        ? dayjs(order.updatedAt).format('DD/MM/YYYY HH:mm:ss')
        : 'N/A',
    };
  });

  // Create worksheet
  const worksheet = XLSX.utils.json_to_sheet(excelData);

  // Set column widths
  const columnWidths = [
    { wch: 5 }, // STT
    { wch: 8 }, // ID
    { wch: 20 }, // Mã đơn hàng
    { wch: 18 }, // Trạng thái
    { wch: 25 }, // Tên khách hàng
    { wch: 15 }, // Số điện thoại
    { wch: 30 }, // Email
    { wch: 40 }, // Địa chỉ
    { wch: 50 }, // Danh sách sản phẩm
    { wch: 20 }, // Số lượng
    { wch: 25 }, // Đơn giá
    { wch: 25 }, // Thành tiền sản phẩm
    { wch: 18 }, // Tổng tiền trước thuế
    { wch: 15 }, // Thuế VAT
    { wch: 18 }, // Tổng tiền sau thuế
    { wch: 20 }, // Ngày tạo
    { wch: 20 }, // Ngày cập nhật
  ];
  worksheet['!cols'] = columnWidths;

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách đơn hàng');

  // Generate filename with current date
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
  const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format
  const filename = `don-hang-${dateStr}-${timeStr}.xlsx`;

  // Write and download file
  XLSX.writeFile(workbook, filename);
};

// Status Update Component
const StatusUpdateButton: React.FC<{
  currentStatus: string;
  orderId: number;
  orderCode: string;
  onStatusUpdate: (id: number, newStatus: string) => Promise<void>;
}> = ({ currentStatus, orderId, orderCode, onStatusUpdate }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [updating, setUpdating] = useState(false);

  const availableOptions = getAvailableStatusOptions(currentStatus);
  const canUpdate = availableOptions.length > 0;

  const handleUpdate = async () => {
    if (!selectedStatus) return;

    setUpdating(true);
    try {
      await onStatusUpdate(orderId, selectedStatus);
      setIsModalVisible(false);
      setSelectedStatus('');
    } catch (error) {
      console.error('Error updating status:', error);
    } finally {
      setUpdating(false);
    }
  };

  const getStatusDescription = (status: string) => {
    const descriptions: Record<string, string> = {
      'Chờ giao hàng': 'Đơn hàng đã được xác nhận và sẵn sàng để giao',
      'Đang giao hàng': 'Đơn hàng đang được vận chuyển đến khách hàng',
      'Đã hoàn thành': 'Đơn hàng đã được giao thành công',
      'Đã hủy': 'Hủy đơn hàng này (không thể hoàn tác)',
    };
    return descriptions[status] || '';
  };

  if (!canUpdate) {
    return (
      <Tooltip title="Không thể thay đổi trạng thái">
        <Button type="text" icon={<SwapOutlined />} size="small" disabled style={{ color: '#ccc' }}>
          Cập nhật
        </Button>
      </Tooltip>
    );
  }

  return (
    <>
      <Tooltip title="Cập nhật trạng thái đơn hàng">
        <Button
          type="text"
          icon={<SwapOutlined />}
          size="small"
          style={{ color: '#1890ff' }}
          onClick={() => setIsModalVisible(true)}
        >
          Cập nhật
        </Button>
      </Tooltip>

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <SwapOutlined style={{ color: '#1890ff' }} />
            <span>Cập nhật trạng thái đơn hàng</span>
          </div>
        }
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setSelectedStatus('');
        }}
        onOk={handleUpdate}
        confirmLoading={updating}
        okText="Cập nhật"
        cancelText="Hủy"
        okButtonProps={{
          disabled: !selectedStatus,
          style: { background: '#1890ff', borderColor: '#1890ff' },
        }}
        width={500}
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ fontWeight: 500, marginBottom: 8 }}>
            Đơn hàng: <span style={{ color: '#1890ff' }}>{orderCode}</span>
          </div>
          <div style={{ marginBottom: 16 }}>
            Trạng thái hiện tại:
            <Tag
              color={getStatusConfig(currentStatus).color}
              icon={getStatusConfig(currentStatus).icon}
              style={{ marginLeft: 8 }}
            >
              {currentStatus}
            </Tag>
          </div>
        </div>

        <div style={{ marginBottom: 16 }}>
          <div style={{ fontWeight: 500, marginBottom: 12 }}>Chọn trạng thái mới:</div>
          <Radio.Group
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {availableOptions.map((option) => (
                <Radio
                  key={option.value}
                  value={option.value}
                  style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    padding: '8px 12px',
                    border: '1px solid #f0f0f0',
                    borderRadius: 8,
                    marginBottom: 8,
                    backgroundColor: selectedStatus === option.value ? '#f6ffed' : '#fafafa',
                  }}
                >
                  <div style={{ marginLeft: 8 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
                      {option.icon}
                      <span style={{ fontWeight: 500 }}>{option.label}</span>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {getStatusDescription(option.value)}
                    </div>
                  </div>
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        </div>
      </Modal>
    </>
  );
};

const OrderList: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<any[]>([]);
  const [allOrders, setAllOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState('Chờ xác nhận');
  const [search, setSearch] = useState('');
  const [exporting, setExporting] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  // Export modal states
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportDateRange, setExportDateRange] = useState<[any, any] | null>(null);
  const [exportStatuses, setExportStatuses] = useState<string[]>([]);

  // Use Strapi's fetch client with authentication
  const { get, put } = useFetchClient();

  // Fetch all orders for tab counts
  const fetchAllOrders = async (silent = false) => {
    try {
      const { data } = await get('/order/orders', {
        params: { page: 1, pageSize: 1000 }, // Get all orders for counting
      });
      setAllOrders(data.data || []);
    } catch (error) {
      console.error('Error fetching all orders:', error);
      if (!silent) {
        message.error('Không thể tải dữ liệu thống kê');
      }
    }
  };

  // Comprehensive refresh function
  const refreshData = async (showMessage = false) => {
    try {
      await Promise.all([fetchOrders(), fetchAllOrders(true)]);
      if (showMessage) {
        message.success('Đã làm mới danh sách đơn hàng');
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      if (showMessage) {
        message.error('Không thể làm mới dữ liệu');
      }
    }
  };

  // Refresh function
  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshData(true);
    setRefreshing(false);
  };

  const fetchOrders = async () => {
    setLoading(true);
    try {
      const params: any = {};
      if (status) params.status = status;
      if (search) params.code = search;
      params.page = page;
      params.pageSize = PAGE_SIZE;

      const { data } = await get('/order/orders', { params });
      setOrders(data.data || []);
      setTotal(data.total || 0);
    } catch (error) {
      console.error('Error fetching orders:', error);
      message.error('Không thể tải danh sách đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
    // eslint-disable-next-line
  }, [status, search, page]);

  useEffect(() => {
    fetchAllOrders();
    // eslint-disable-next-line
  }, []);

  // Refresh badge counts when switching tabs or after status changes
  useEffect(() => {
    // Small delay to ensure the status change has been processed
    const timeoutId = setTimeout(() => {
      fetchAllOrders(true);
    }, 100);

    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line
  }, [status]);

  const handleStatusChange = async (id: number, newStatus: string) => {
    try {
      await put(`/order/orders/${id}/status`, { status: newStatus });
      message.success('Cập nhật trạng thái thành công');
      // Refresh both filtered orders and all orders for badge counts
      await refreshData();
    } catch (error) {
      console.error('Error updating order status:', error);
      message.error('Không thể cập nhật trạng thái đơn hàng');
    }
  };

  const handleExport = () => {
    setExportModalVisible(true);
    // Set default values
    setExportStatuses([status].filter(Boolean)); // Current status if any
    setExportDateRange(null);
  };

  const handleExportConfirm = async () => {
    setExporting(true);
    try {
      const queryParams: any = {
        page: 1,
        pageSize: 10000, // Get all orders
      };

      // Add date range if selected
      if (exportDateRange && exportDateRange[0] && exportDateRange[1]) {
        queryParams.startDate = exportDateRange[0].format('YYYY-MM-DD');
        queryParams.endDate = exportDateRange[1].format('YYYY-MM-DD');
      }

      // Add selected statuses
      if (exportStatuses.length > 0) {
        // For multiple statuses, we'll filter client-side since backend expects single status
        if (exportStatuses.length === 1) {
          queryParams.status = exportStatuses[0];
        }
      }

      // Add search term if exists
      if (search) {
        queryParams.code = search;
      }

      // Use authenticated fetch client to get orders data
      const { data: ordersData } = await get('/order/orders', { params: queryParams });
      let ordersToExport = ordersData.data || [];

      // Filter by multiple statuses if needed (client-side filtering)
      if (exportStatuses.length > 1) {
        ordersToExport = ordersToExport.filter((order: any) =>
          exportStatuses.includes(order.statusOrder)
        );
      }

      // Filter by date range if needed (client-side filtering for more precise control)
      if (exportDateRange && exportDateRange[0] && exportDateRange[1]) {
        const startDate = exportDateRange[0].startOf('day');
        const endDate = exportDateRange[1].endOf('day');
        ordersToExport = ordersToExport.filter((order: any) => {
          const orderDate = dayjs(order.createdAt);
          return orderDate.isAfter(startDate) && orderDate.isBefore(endDate);
        });
      }

      if (ordersToExport.length === 0) {
        message.warning('Không có đơn hàng nào phù hợp với bộ lọc đã chọn');
        setExportModalVisible(false);
        return;
      }

      // Create Excel file using client-side library
      await createAndDownloadExcel(ordersToExport);

      setExportModalVisible(false);
      message.success(`Đã tải xuống file Excel với ${ordersToExport.length} đơn hàng!`);
    } catch (error) {
      console.error('Error exporting orders:', error);
      const errorMessage = error instanceof Error ? error.message : 'Lỗi không xác định';
      message.error(`Không thể xuất file Excel: ${errorMessage}`);
    } finally {
      setExporting(false);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (value: number) => (
        <Badge count={value} style={{ backgroundColor: '#f0f0f0', color: '#666' }} />
      ),
    },
    {
      title: 'Mã đơn hàng',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (value: string) => <div style={{ fontWeight: 600, color: '#1e293b' }}>{value}</div>,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusOrder',
      key: 'statusOrder',
      width: 160,
      render: (value: string) => {
        const config = getStatusConfig(value);
        return (
          <Tag
            color={config.color}
            icon={config.icon}
            style={{
              margin: 0,
              padding: '6px 12px',
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              width: 'fit-content',
            }}
          >
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'priceAfterTax',
      key: 'priceAfterTax',
      width: 150,
      render: (value: number) => (
        <div style={{ fontWeight: 600, color: '#1e293b', fontSize: '14px' }}>
          {formatCurrency(value)}
        </div>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (value: string) => {
        if (!value) return '-';
        const date = new Date(value);
        return (
          <div>
            <div style={{ fontWeight: 500 }}>{date.toLocaleDateString('vi-VN')}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {date.toLocaleTimeString('vi-VN')}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Hành động',
      key: 'action',
      width: 200,
      render: (_: any, record: any) => (
        <Space size="small">
          <Tooltip title="Xem chi tiết">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              style={{ color: '#1890ff' }}
              onClick={() => navigate(`orders/${record.id}`)}
            >
              Chi tiết
            </Button>
          </Tooltip>

          <StatusUpdateButton
            currentStatus={record.statusOrder}
            orderId={record.id}
            orderCode={record.code}
            onStatusUpdate={handleStatusChange}
          />
        </Space>
      ),
    },
  ];

  return (
    <div
      className="order-plugin order-list-container"
      style={{
        minHeight: '100vh',
        background: '#f8fafc',
        padding: '24px',
      }}
    >
      {/* Header Section */}
      <Card
        style={{
          marginBottom: 24,
          borderRadius: 16,
          border: '1px solid #e2e8f0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          background: '#ffffff',
          padding: '8px 0',
        }}
      >
        <Row align="middle" justify="space-between" gutter={[24, 16]}>
          <Col xs={24} lg={14}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
              <div
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 16,
                  background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: 28,
                  boxShadow: '0 4px 12px rgba(30, 41, 59, 0.2)',
                }}
              >
                <ShoppingCartOutlined />
              </div>
              <div>
                <Title
                  level={2}
                  style={{
                    margin: 0,
                    marginBottom: 4,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    fontWeight: 700,
                    color: '#1e293b',
                    fontSize: 24,
                    lineHeight: 1.2,
                  }}
                >
                  Quản lý đơn hàng
                </Title>
                <Paragraph
                  style={{
                    margin: 0,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                    fontSize: 16,
                    color: '#64748b',
                    lineHeight: 1.4,
                  }}
                >
                  Theo dõi và quản lý tất cả đơn hàng của bạn một cách hiệu quả
                </Paragraph>
              </div>
            </div>
          </Col>
          <Col xs={24} lg={10}>
            <Row gutter={[16, 16]} justify="end">
              <Col xs={12} sm={8}>
                <div
                  style={{
                    textAlign: 'center',
                    padding: '12px',
                    background: '#f8fafc',
                    borderRadius: 12,
                    border: '1px solid #e2e8f0',
                    height: 48,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      fontSize: 20,
                      fontWeight: 700,
                      color: '#1e293b',
                      lineHeight: 1,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    {allOrders.length}
                  </div>
                  <div
                    style={{
                      fontSize: 12,
                      color: '#64748b',
                      fontWeight: 500,
                      lineHeight: 1,
                      marginTop: 2,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    Tổng đơn hàng
                  </div>
                </div>
              </Col>
              <Col xs={12} sm={8}>
                <div
                  style={{
                    textAlign: 'center',
                    padding: '12px',
                    background: '#fef3c7',
                    borderRadius: 12,
                    border: '1px solid #fbbf24',
                    height: 48,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <div
                    style={{
                      fontSize: 20,
                      fontWeight: 700,
                      color: '#d97706',
                      lineHeight: 1,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    {allOrders.filter((o) => o.statusOrder === 'Chờ xác nhận').length}
                  </div>
                  <div
                    style={{
                      fontSize: 12,
                      color: '#d97706',
                      fontWeight: 500,
                      lineHeight: 1,
                      marginTop: 2,
                      fontFamily: "'Be Vietnam Pro', sans-serif",
                    }}
                  >
                    Chờ duyệt
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  loading={refreshing}
                  block
                  size="large"
                  style={{
                    borderRadius: 12,
                    height: 48,
                    background: '#1e293b',
                    borderColor: '#1e293b',
                    boxShadow: '0 2px 8px rgba(30, 41, 59, 0.15)',
                    fontWeight: 600,
                    fontSize: 16,
                    fontFamily: "'Be Vietnam Pro', sans-serif",
                  }}
                >
                  Làm mới
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>
      {/* Status Tabs Section */}
      <Card
        className="order-card"
        style={{
          marginBottom: 24,
          borderRadius: 12,
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          background: '#ffffff',
        }}
      >
        <Tabs
          activeKey={status}
          onChange={(key: string) => {
            setStatus(key);
            setPage(1); // Reset to first page when changing status
          }}
          items={getTabItems(allOrders)}
          size="large"
          style={{
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
          tabBarStyle={{
            marginBottom: 16,
            borderBottom: '2px solid #f0f0f0',
          }}
        />

        {/* Search and Export Section */}
        <Row gutter={[16, 16]} align="middle" style={{ marginTop: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <div style={{ marginBottom: 8 }}>
              <SearchOutlined style={{ marginRight: 8, color: '#64748b' }} />
              <span style={{ fontWeight: 500, color: '#1e293b' }}>Tìm kiếm đơn hàng</span>
            </div>
            <Input
              value={search}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
              placeholder="Nhập mã đơn hàng..."
              allowClear
              size="large"
              prefix={<SearchOutlined style={{ color: '#ccc' }} />}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <div style={{ marginBottom: 8 }}>
              <span style={{ fontWeight: 500, color: '#1e293b' }}>Thao tác</span>
            </div>
            <Space>
              <Button
                icon={<FileExcelOutlined />}
                onClick={handleExport}
                loading={exporting}
                size="large"
                style={{
                  borderRadius: 8,
                  background: '#059669',
                  borderColor: '#059669',
                  color: 'white',
                  boxShadow: '0 1px 3px rgba(5, 150, 105, 0.2)',
                }}
              >
                Xuất Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
      {/* Table Section */}
      <Card
        className="order-card"
        style={{
          borderRadius: 12,
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          background: '#ffffff',
          overflow: 'hidden',
        }}
      >
        <Spin
          spinning={loading}
          tip="Đang tải dữ liệu..."
          style={{
            fontSize: 16,
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
        >
          {orders.length === 0 && !loading ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span style={{ color: '#666', fontSize: 16 }}>Không có đơn hàng nào</span>
              }
              style={{ padding: '60px 0' }}
            />
          ) : (
            <Table
              columns={columns}
              dataSource={orders}
              rowKey="id"
              pagination={false}
              scroll={{ x: 1200 }}
              style={{
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
              rowClassName={(_, index) => (index % 2 === 0 ? 'table-row-light' : 'table-row-dark')}
            />
          )}
        </Spin>

        {/* Pagination */}
        {total > 0 && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: 24,
              padding: '16px 0',
              borderTop: '1px solid #f0f0f0',
            }}
          >
            <div style={{ color: '#666', fontSize: 14 }}>
              Hiển thị {(page - 1) * PAGE_SIZE + 1} - {Math.min(page * PAGE_SIZE, total)} trong tổng
              số {total} đơn hàng
            </div>
            <Pagination
              current={page}
              pageSize={PAGE_SIZE}
              total={total}
              onChange={setPage}
              showSizeChanger={false}
              showQuickJumper
              style={{
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            />
          </div>
        )}
      </Card>

      {/* Export Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <FileExcelOutlined style={{ color: '#059669' }} />
            <span>Xuất Excel đơn hàng</span>
          </div>
        }
        open={exportModalVisible}
        onCancel={() => {
          setExportModalVisible(false);
          setExportDateRange(null);
          setExportStatuses([]);
        }}
        onOk={handleExportConfirm}
        confirmLoading={exporting}
        okText="Xuất Excel"
        cancelText="Hủy"
        okButtonProps={{
          style: { background: '#059669', borderColor: '#059669' },
        }}
        width={600}
      >
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontWeight: 500, marginBottom: 12, color: '#1e293b' }}>
            Chọn khoảng thời gian (tùy chọn):
          </div>
          <RangePicker
            value={exportDateRange}
            onChange={setExportDateRange}
            style={{ width: '100%' }}
            size="large"
            placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
            format="DD/MM/YYYY"
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            Để trống để xuất tất cả đơn hàng theo thời gian
          </div>
        </div>

        <div style={{ marginBottom: 24 }}>
          <div style={{ fontWeight: 500, marginBottom: 12, color: '#1e293b' }}>
            Chọn trạng thái đơn hàng:
          </div>
          <Checkbox.Group
            value={exportStatuses}
            onChange={setExportStatuses}
            style={{ width: '100%' }}
          >
            <Row gutter={[16, 12]}>
              {statusOptions
                .filter((option) => option.value !== '') // Exclude "Tất cả"
                .map((option) => (
                  <Col span={12} key={option.value}>
                    <Checkbox
                      value={option.value}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px 12px',
                        border: '1px solid #f0f0f0',
                        borderRadius: 8,
                        backgroundColor: exportStatuses.includes(option.value)
                          ? '#f6ffed'
                          : '#fafafa',
                        width: '100%',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginLeft: 8 }}>
                        {option.icon}
                        <span style={{ fontWeight: 500 }}>{option.label}</span>
                      </div>
                    </Checkbox>
                  </Col>
                ))}
            </Row>
          </Checkbox.Group>
          <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
            Để trống để xuất tất cả trạng thái
          </div>
        </div>

        {search && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ fontWeight: 500, marginBottom: 8, color: '#1e293b' }}>
              Từ khóa tìm kiếm hiện tại:
            </div>
            <Tag color="blue" style={{ padding: '4px 12px', fontSize: '13px' }}>
              {search}
            </Tag>
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              Sẽ áp dụng bộ lọc tìm kiếm hiện tại
            </div>
          </div>
        )}

        <div
          style={{
            background: '#f8fafc',
            padding: '12px 16px',
            borderRadius: 8,
            border: '1px solid #e2e8f0',
          }}
        >
          <div style={{ fontSize: '13px', color: '#64748b', lineHeight: 1.5 }}>
            <strong>Lưu ý:</strong> File Excel sẽ chứa thông tin chi tiết về đơn hàng bao gồm: mã
            đơn hàng, thông tin khách hàng, sản phẩm, giá trị đơn hàng và trạng thái.
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default OrderList;
