import type { Core } from '@strapi/strapi';
import * as XLSX from 'xlsx';

const productController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listProducts(ctx: any) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        category,
        status,
        dateFrom,
        dateTo,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = ctx.query;

      // Build filters
      const filters: any = {};

      // Sanitize search term to prevent issues with special characters
      if (search) {
        const sanitizedSearch = search
          .toString()
          .trim()
          .replace(/[<>'"\\{}[\]|&]/g, '') // Remove potentially problematic characters
          .substring(0, 100); // Limit length

        if (sanitizedSearch.length > 0) {
          filters.$or = [
            { name: { $containsi: sanitizedSearch } },
            { mo_ta: { $containsi: sanitizedSearch } },
          ];
        }
      }

      if (category) {
        filters.category = category;
      }

      if (status !== undefined) {
        filters.isActive = status === 'true';
      }

      // Add date range filters
      if (dateFrom || dateTo) {
        filters.createdAt = {};

        if (dateFrom) {
          // Start of the day for dateFrom
          const startDate = new Date(dateFrom);
          startDate.setHours(0, 0, 0, 0);
          filters.createdAt.$gte = startDate;
        }

        if (dateTo) {
          // End of the day for dateTo
          const endDate = new Date(dateTo);
          endDate.setHours(23, 59, 59, 999);
          filters.createdAt.$lte = endDate;
        }
      }

      // Get products with pagination
      const products = await strapi.entityService.findMany('api::san-pham.san-pham', {
        filters,
        sort: { [sortBy]: sortOrder },
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        },
        populate: {
          hinh_anh: true,
          danh_muc: true,
          thuong_hieu: true,
        },
      });

      // Get total count for pagination
      const total = await strapi.entityService.count('api::san-pham.san-pham', {
        filters,
      });

      // Calculate stats
      const stats = await strapi.db.query('api::san-pham.san-pham').findMany({
        select: ['id', 'so_luong_ton_kho', 'isActive'],
      });

      const totalProducts = stats.length;
      const inStockProducts = stats.filter((p) => p.so_luong_ton_kho > 0).length;
      const outOfStockProducts = stats.filter((p) => p.so_luong_ton_kho === 0).length;
      const activeProducts = stats.filter((p) => p.isActive).length;

      ctx.body = {
        data: products,
        meta: {
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            pageCount: Math.ceil(total / parseInt(pageSize)),
            total,
          },
          stats: {
            totalProducts,
            inStockProducts,
            outOfStockProducts,
            activeProducts,
          },
        },
      };
    } catch (error) {
      ctx.throw(500, `Failed to fetch products: ${error.message}`);
    }
  },

  async getProductDetail(ctx: any) {
    try {
      const { id } = ctx.params;

      const product = await strapi.entityService.findOne('api::san-pham.san-pham', id, {
        populate: {
          hinh_anh: true,
          danh_muc: true,
          thuong_hieu: true,
        },
      });

      if (!product) {
        return ctx.throw(404, 'Product not found');
      }

      ctx.body = { data: product };
    } catch (error) {
      ctx.throw(500, `Failed to fetch product: ${error.message}`);
    }
  },

  async createProduct(ctx: any) {
    try {
      const { data } = ctx.request.body;

      const product = await strapi.entityService.create('api::san-pham.san-pham', {
        data: {
          ...data,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        populate: {
          hinh_anh: true,
          danh_muc: true,
          thuong_hieu: true,
        },
      });

      ctx.body = { data: product };
    } catch (error) {
      ctx.throw(500, `Failed to create product: ${error.message}`);
    }
  },

  async updateProduct(ctx: any) {
    try {
      const { id } = ctx.params;
      const { data } = ctx.request.body;

      const product = await strapi.entityService.update('api::san-pham.san-pham', id, {
        data: {
          ...data,
          updatedAt: new Date(),
        },
        populate: {
          hinh_anh: true,
          danh_muc: true,
          thuong_hieu: true,
        },
      });

      if (!product) {
        return ctx.throw(404, 'Product not found');
      }

      ctx.body = { data: product };
    } catch (error) {
      ctx.throw(500, `Failed to update product: ${error.message}`);
    }
  },

  async deleteProduct(ctx: any) {
    try {
      const { id } = ctx.params;

      const product = await strapi.entityService.delete('api::san-pham.san-pham', id);

      if (!product) {
        return ctx.throw(404, 'Product not found');
      }

      ctx.body = { data: { id, deleted: true } };
    } catch (error) {
      ctx.throw(500, `Failed to delete product: ${error.message}`);
    }
  },

  async updateProductStatus(ctx: any) {
    try {
      const { id } = ctx.params;
      const { isActive } = ctx.request.body;

      const product = await strapi.entityService.update('api::san-pham.san-pham', id, {
        data: {
          isActive: isActive,
        } as any,
      });

      if (!product) {
        return ctx.throw(404, 'Product not found');
      }

      ctx.body = { data: product };
    } catch (error) {
      ctx.throw(500, `Failed to update product status: ${error.message}`);
    }
  },

  async getProductCategories(ctx: any) {
    try {
      console.log('Fetching categories...');

      // Check if only active categories are requested
      const { activeOnly } = ctx.query;
      const filters: any = {};

      if (activeOnly === 'true') {
        filters.isActive = true;
      }

      const categories = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        {
          filters,
          sort: { name: 'asc' },
          populate: {
            image: true,
          },
        }
      );

      console.log('Categories found:', categories?.length || 0);
      console.log('Active only filter:', activeOnly === 'true');
      ctx.body = { data: categories };
    } catch (error) {
      console.error('Error fetching categories:', error);
      ctx.throw(500, `Failed to fetch categories: ${error.message}`);
    }
  },

  async getProductBrands(ctx: any) {
    try {
      console.log('Fetching brands...');

      // Check if only active brands are requested
      const { activeOnly } = ctx.query;
      const filters: any = {};

      if (activeOnly === 'true') {
        filters.isActive = true;
      }

      const brands = await strapi.entityService.findMany('api::thuong-hieu.thuong-hieu', {
        filters,
        sort: { name: 'asc' },
        populate: {
          logo: true,
        },
      });

      console.log('Brands found:', brands?.length || 0);
      console.log('Active only filter:', activeOnly === 'true');
      ctx.body = { data: brands };
    } catch (error) {
      console.error('Error fetching brands:', error);
      ctx.throw(500, `Failed to fetch brands: ${error.message}`);
    }
  },

  async bulkUpdateProducts(ctx: any) {
    try {
      const { productIds, updates } = ctx.request.body;

      const results = await Promise.all(
        productIds.map(async (id: string) => {
          try {
            const product = await strapi.entityService.update('api::san-pham.san-pham', id, {
              data: {
                ...updates,
                updatedAt: new Date(),
              } as any,
            });
            return { id, success: true, data: product };
          } catch (error: any) {
            return { id, success: false, error: error.message };
          }
        })
      );

      ctx.body = { data: results };
    } catch (error: any) {
      ctx.throw(500, `Failed to bulk update products: ${error.message}`);
    }
  },

  async exportProducts(ctx: any) {
    try {
      const { format = 'csv', filters = {} } = ctx.query;

      // Build proper filters from query parameters
      const builtFilters: any = {};

      // Handle search filter with sanitization
      if (filters.search) {
        const sanitizedSearch = filters.search
          .toString()
          .trim()
          .replace(/[<>'"\\{}[\]|&]/g, '') // Remove potentially problematic characters
          .substring(0, 100); // Limit length

        if (sanitizedSearch.length > 0) {
          builtFilters.$or = [
            { name: { $containsi: sanitizedSearch } },
            { mo_ta: { $containsi: sanitizedSearch } },
          ];
        }
      }

      // Handle category filter
      if (filters.category) {
        builtFilters.category = filters.category;
      }

      // Handle status filter
      if (filters.status !== undefined) {
        builtFilters.isActive = filters.status === 'true';
      }

      // Handle date range filters
      if (filters.dateFrom || filters.dateTo) {
        builtFilters.createdAt = {};

        if (filters.dateFrom) {
          const startDate = new Date(filters.dateFrom);
          startDate.setHours(0, 0, 0, 0);
          builtFilters.createdAt.$gte = startDate;
        }

        if (filters.dateTo) {
          const endDate = new Date(filters.dateTo);
          endDate.setHours(23, 59, 59, 999);
          builtFilters.createdAt.$lte = endDate;
        }
      }

      const products = await strapi.entityService.findMany('api::san-pham.san-pham', {
        filters: builtFilters,
        populate: {
          danh_muc: true,
          thuong_hieu: true,
        },
      });

      // Transform data for export
      const exportData = (products as any[]).map((product: any) => ({
        'Mã sản phẩm': product.sku || '',
        'Tên sản phẩm': product.name || '',
        'Danh mục': product.danh_muc?.name || '',
        'Thương hiệu': product.thuong_hieu?.name || '',
        'Giá bán': product.gia_ban || 0,
        'Giá gốc': product.gia_goc || 0,
        'Tồn kho': product.so_luong_ton_kho || 0,
        'Đã bán': product.da_ban || 0,
        'Trạng thái': product.isActive ? 'Hoạt động' : 'Không hoạt động',
        'Ngày tạo': product.createdAt
          ? new Date(product.createdAt).toLocaleDateString('vi-VN')
          : '',
      }));

      ctx.body = { data: exportData };
    } catch (error: any) {
      ctx.throw(500, `Failed to export products: ${error.message}`);
    }
  },

  async seedCategoriesAndBrands(ctx: any) {
    try {
      console.log('Starting to seed categories and brands...');

      // Sample categories
      const categories = [
        { name: 'Điện thoại' },
        { name: 'Laptop' },
        { name: 'Tablet' },
        { name: 'Phụ kiện' },
        { name: 'Đồng hồ thông minh' },
      ];

      // Sample brands
      const brands = [
        {
          name: 'Apple',
          description: 'Thương hiệu công nghệ hàng đầu thế giới',
          website: 'https://apple.com',
          isActive: true,
        },
        {
          name: 'Samsung',
          description: 'Tập đoàn công nghệ đa quốc gia Hàn Quốc',
          website: 'https://samsung.com',
          isActive: true,
        },
        {
          name: 'Xiaomi',
          description: 'Thương hiệu điện tử tiêu dùng Trung Quốc',
          website: 'https://mi.com',
          isActive: true,
        },
        {
          name: 'Oppo',
          description: 'Thương hiệu điện thoại thông minh',
          website: 'https://oppo.com',
          isActive: true,
        },
        {
          name: 'Vivo',
          description: 'Thương hiệu công nghệ di động',
          website: 'https://vivo.com',
          isActive: true,
        },
      ];

      // Create categories (skip if already exists)
      const createdCategories = [];
      for (const category of categories) {
        try {
          const existing = await strapi.entityService.findMany(
            'api::danh-muc-san-pham.danh-muc-san-pham',
            {
              filters: { name: category.name },
              limit: 1,
            }
          );

          if (!existing || existing.length === 0) {
            const created = await strapi.entityService.create(
              'api::danh-muc-san-pham.danh-muc-san-pham',
              {
                data: category,
              }
            );
            createdCategories.push(created);
            console.log(`Created category: ${category.name}`);
          } else {
            console.log(`Category already exists: ${category.name}`);
          }
        } catch (error) {
          console.error(`Error creating category ${category.name}:`, error);
        }
      }

      // Create brands (skip if already exists)
      const createdBrands = [];
      for (const brand of brands) {
        try {
          const existing = await strapi.entityService.findMany('api::thuong-hieu.thuong-hieu', {
            filters: { name: brand.name },
            limit: 1,
          });

          if (!existing || existing.length === 0) {
            const created = await strapi.entityService.create('api::thuong-hieu.thuong-hieu', {
              data: brand,
            });
            createdBrands.push(created);
            console.log(`Created brand: ${brand.name}`);
          } else {
            console.log(`Brand already exists: ${brand.name}`);
          }
        } catch (error) {
          console.error(`Error creating brand ${brand.name}:`, error);
        }
      }

      ctx.body = {
        success: true,
        message: 'Seed data completed',
        data: {
          categoriesCreated: createdCategories.length,
          brandsCreated: createdBrands.length,
          categories: createdCategories,
          brands: createdBrands,
        },
      };
    } catch (error: any) {
      console.error('Error seeding data:', error);
      ctx.throw(500, `Failed to seed data: ${error.message}`);
    }
  },

  async createProductCategory(ctx: any) {
    try {
      const requestData = ctx.request.body.data || ctx.request.body;
      console.log('Category request data:', requestData);

      const { name, image, isActive } = requestData;

      if (!name) {
        ctx.throw(400, 'Category name is required');
      }

      // Check if category already exists
      const existingCategory = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        {
          filters: { name: { $eqi: name } },
          limit: 1,
        }
      );

      console.log('Checking for existing category with name:', name);
      console.log('Found existing categories:', existingCategory);

      if (existingCategory && existingCategory.length > 0) {
        console.log('Duplicate category found:', existingCategory[0]);
        ctx.throw(400, `Category with this name already exists: "${existingCategory[0].name}"`);
      }

      const category = await strapi.entityService.create(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        {
          data: {
            name,
            image: image || null,
            isActive: isActive !== undefined ? isActive : true,
          },
        }
      );

      ctx.body = {
        success: true,
        data: category,
        message: 'Category created successfully',
      };
    } catch (error: any) {
      console.error('Error creating category:', error);
      ctx.throw(500, `Failed to create category: ${error.message}`);
    }
  },

  async updateProductCategory(ctx: any) {
    try {
      const { id } = ctx.params;
      const requestData = ctx.request.body.data || ctx.request.body;
      console.log('Update category request data:', requestData);

      const { name, image, isActive } = requestData;

      // Prepare update data
      const updateData: any = {
        name,
        isActive: isActive !== undefined ? isActive : true,
      };

      // Only update image if provided
      if (image !== undefined) {
        updateData.image = image;
      }

      if (!name) {
        ctx.throw(400, 'Category name is required');
      }

      // Check if another category with the same name exists (excluding current one)
      const existingCategory = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        {
          filters: {
            name: { $eqi: name },
            id: { $ne: id },
          },
          limit: 1,
        }
      );

      if (existingCategory && existingCategory.length > 0) {
        ctx.throw(400, 'Category with this name already exists');
      }

      const category = await strapi.entityService.update(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        id,
        {
          data: updateData,
          populate: {
            image: true,
          },
        }
      );

      ctx.body = {
        success: true,
        data: category,
        message: 'Category updated successfully',
      };
    } catch (error: any) {
      console.error('Error updating category:', error);
      ctx.throw(500, `Failed to update category: ${error.message}`);
    }
  },

  async deleteProductCategory(ctx: any) {
    try {
      const { id } = ctx.params;

      // Check if category is being used by any products
      const productsUsingCategory = await strapi.entityService.findMany('api::san-pham.san-pham', {
        filters: { danh_muc: { id } },
        limit: 1,
      });

      if (productsUsingCategory && productsUsingCategory.length > 0) {
        ctx.throw(400, 'Cannot delete category that is being used by products');
      }

      await strapi.entityService.delete('api::danh-muc-san-pham.danh-muc-san-pham', id);

      ctx.body = {
        success: true,
        message: 'Category deleted successfully',
      };
    } catch (error: any) {
      console.error('Error deleting category:', error);
      ctx.throw(500, `Failed to delete category: ${error.message}`);
    }
  },

  async createProductBrand(ctx: any) {
    try {
      const requestData = ctx.request.body.data || ctx.request.body;
      console.log('Brand request data:', requestData);
      const { name, description, website, logo } = requestData;

      if (!name) {
        ctx.throw(400, 'Brand name is required');
      }

      // Check if brand already exists
      const existingBrand = await strapi.entityService.findMany('api::thuong-hieu.thuong-hieu', {
        filters: { name: { $eqi: name } },
        limit: 1,
      });

      if (existingBrand && existingBrand.length > 0) {
        ctx.throw(400, 'Brand with this name already exists');
      }

      const brand = await strapi.entityService.create('api::thuong-hieu.thuong-hieu', {
        data: {
          name,
          description: description || '',
          website: website || '',
          logo: logo || null,
          isActive: true,
        },
      });

      ctx.body = {
        success: true,
        data: brand,
        message: 'Brand created successfully',
      };
    } catch (error: any) {
      console.error('Error creating brand:', error);
      ctx.throw(500, `Failed to create brand: ${error.message}`);
    }
  },

  async updateProductBrand(ctx: any) {
    try {
      const { id } = ctx.params;
      const requestData = ctx.request.body.data || ctx.request.body;
      const { name, description, website, logo, isActive } = requestData;

      if (!name) {
        ctx.throw(400, 'Brand name is required');
      }

      // Check if another brand with the same name exists (excluding current one)
      const existingBrand = await strapi.entityService.findMany('api::thuong-hieu.thuong-hieu', {
        filters: {
          name: { $eqi: name },
          id: { $ne: id },
        },
        limit: 1,
      });

      if (existingBrand && existingBrand.length > 0) {
        ctx.throw(400, 'Brand with this name already exists');
      }

      const brand = await strapi.entityService.update('api::thuong-hieu.thuong-hieu', id, {
        data: {
          name,
          description: description || '',
          website: website || '',
          logo: logo || null,
          isActive: isActive !== undefined ? isActive : true,
        } as any,
        populate: {
          logo: true,
        },
      });

      ctx.body = {
        success: true,
        data: brand,
        message: 'Brand updated successfully',
      };
    } catch (error: any) {
      console.error('Error updating brand:', error);
      ctx.throw(500, `Failed to update brand: ${error.message}`);
    }
  },

  async deleteProductBrand(ctx: any) {
    try {
      const { id } = ctx.params;

      // Check if brand is being used by any products
      const productsUsingBrand = await strapi.entityService.findMany('api::san-pham.san-pham', {
        filters: { thuong_hieu: { id } },
        limit: 1,
      });

      if (productsUsingBrand && productsUsingBrand.length > 0) {
        ctx.throw(400, 'Cannot delete brand that is being used by products');
      }

      await strapi.entityService.delete('api::thuong-hieu.thuong-hieu', id);

      ctx.body = {
        success: true,
        message: 'Brand deleted successfully',
      };
    } catch (error: any) {
      console.error('Error deleting brand:', error);
      ctx.throw(500, `Failed to delete brand: ${error.message}`);
    }
  },

  async debugCategories(ctx: any) {
    try {
      console.log('🔍 Debug: Listing all categories...');

      // Get all categories
      const allCategories = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        {
          populate: {
            image: true,
          },
        }
      );

      console.log('All categories in database:', allCategories);

      ctx.body = {
        success: true,
        data: {
          totalCategories: allCategories?.length || 0,
          categories: allCategories || [],
        },
        message: 'Categories debug info',
      };
    } catch (error: any) {
      console.error('Error debugging categories:', error);
      ctx.throw(500, `Failed to debug categories: ${error.message}`);
    }
  },

  async checkCategorySchema(ctx: any) {
    try {
      console.log('🔍 Checking category schema...');

      // Check if isActive field exists in the model
      const categoryModel = strapi.contentTypes['api::danh-muc-san-pham.danh-muc-san-pham'];
      const hasIsActiveInModel =
        categoryModel && categoryModel.attributes && categoryModel.attributes.isActive;

      // Check database table structure
      const knex = strapi.db.connection;
      let hasIsActiveInDB = false;
      let tableExists = false;

      try {
        tableExists = await knex.schema.hasTable('danh_muc_san_phams');
        if (tableExists) {
          hasIsActiveInDB = await knex.schema.hasColumn('danh_muc_san_phams', 'isActive');

          if (!hasIsActiveInDB) {
            console.log('🔧 Adding isActive column to database...');

            await knex.schema.alterTable('danh_muc_san_phams', (table) => {
              table.boolean('isActive').defaultTo(true).notNullable();
            });

            // Update existing records
            await knex('danh_muc_san_phams').update({ isActive: true });

            hasIsActiveInDB = true;
            console.log('✅ Added isActive column and updated existing records');
          }
        }
      } catch (dbError) {
        console.error('Database error:', dbError);
      }

      // Test fetching categories
      let sampleCategories: any[] = [];
      try {
        const result = await strapi.entityService.findMany(
          'api::danh-muc-san-pham.danh-muc-san-pham',
          { limit: 3 }
        );
        sampleCategories = Array.isArray(result) ? result : [result];
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
      }

      ctx.body = {
        success: true,
        data: {
          modelHasIsActive: !!hasIsActiveInModel,
          databaseHasIsActive: hasIsActiveInDB,
          tableExists,
          sampleCategories,
          modelAttributes: categoryModel?.attributes ? Object.keys(categoryModel.attributes) : [],
        },
        message: 'Schema check completed',
      };
    } catch (error: any) {
      console.error('Error checking schema:', error);
      ctx.throw(500, `Failed to check schema: ${error.message}`);
    }
  },

  async importProducts(ctx: any) {
    try {
      // Check if file is uploaded via FormData
      const file = ctx.request.files?.file;

      if (!file) {
        ctx.throw(400, 'No file uploaded');
      }

      console.log('File info:', {
        name: file.name,
        type: file.type,
        size: file.size,
        path: file.path,
        filepath: file.filepath,
        keys: Object.keys(file),
      });

      // Read the uploaded file
      let workbook;
      let data;

      try {
        const fs = require('fs');
        let buffer;

        // Try different ways to access the file content
        if (file.path && fs.existsSync(file.path)) {
          buffer = fs.readFileSync(file.path);
        } else if (file.filepath && fs.existsSync(file.filepath)) {
          buffer = fs.readFileSync(file.filepath);
        } else if (file.buffer) {
          buffer = file.buffer;
        } else if (
          file._writeStream &&
          file._writeStream.path &&
          fs.existsSync(file._writeStream.path)
        ) {
          buffer = fs.readFileSync(file._writeStream.path);
        } else {
          // Try to find any path-like property
          const pathProps = ['tmpPath', 'tempPath', 'filePath', 'fullPath'];
          let foundPath = null;

          for (const prop of pathProps) {
            if (file[prop] && fs.existsSync(file[prop])) {
              foundPath = file[prop];
              break;
            }
          }

          if (foundPath) {
            buffer = fs.readFileSync(foundPath);
          } else {
            throw new Error(
              'Cannot access file content. Available properties: ' + Object.keys(file).join(', ')
            );
          }
        }

        // Read Excel file from buffer
        workbook = XLSX.read(buffer, { type: 'buffer' });

        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        data = XLSX.utils.sheet_to_json(worksheet);
      } catch (error) {
        console.error('Error reading file:', error);
        console.error('File object keys:', Object.keys(file));
        ctx.throw(400, `Invalid file format. Error: ${error.message}`);
      }

      if (!data || data.length === 0) {
        ctx.throw(400, 'File is empty or invalid format');
      }

      console.log('Data preview:', data.slice(0, 2)); // Log first 2 rows for debugging

      let imported = 0;
      let errors = [];

      // Process each row
      for (let i = 0; i < data.length; i++) {
        const row = data[i];

        try {
          // Map Excel columns to product fields (using Vietnamese headers)
          const productData: any = {
            name: row['Tên sản phẩm'] || row['name'] || '',
            gia_ban: parseFloat(row['Giá bán'] || row['sales_price'] || row['gia_ban'] || 0),
            gia_goc: parseFloat(row['Giá gốc'] || row['purchase_price'] || row['gia_goc'] || 0),
            so_luong_ton_kho: parseInt(
              row['Tồn kho'] || row['opening_stock'] || row['so_luong_ton_kho'] || 0
            ),
            mo_ta: row['Mô tả'] || row['description'] || row['mo_ta'] || '',
            hot: Boolean(
              row['Sản phẩm nổi bật'] === 'Có' || row['hot'] === true || row['hot'] === 'true'
            ),
            isActive: Boolean(row['Trạng thái'] !== 'Không hoạt động' && row['isActive'] !== false),
          };

          // Handle category mapping
          const categoryName = row['Danh mục'] || row['custom_collections'] || '';
          if (categoryName) {
            // Find category by name
            const category = await strapi.entityService.findMany(
              'api::danh-muc-san-pham.danh-muc-san-pham',
              {
                filters: { name: { $eqi: categoryName } },
                limit: 1,
              }
            );
            if (category && category.length > 0) {
              productData.danh_muc = category[0].id;
            }
          }

          // Handle brand mapping
          const brandName = row['Thương hiệu'] || row['brand_id'] || '';
          if (brandName) {
            // Find brand by name
            const brand = await strapi.entityService.findMany('api::thuong-hieu.thuong-hieu', {
              filters: { name: { $eqi: brandName } },
              limit: 1,
            });
            if (brand && brand.length > 0) {
              productData.thuong_hieu = brand[0].id;
            }
          }

          // Validate required fields
          if (!productData.name) {
            errors.push(`Row ${i + 1}: Product name is required`);
            continue;
          }

          if (productData.gia_ban <= 0) {
            errors.push(`Row ${i + 1}: Valid selling price is required`);
            continue;
          }

          // Check if product already exists
          const existingProduct = await strapi.entityService.findMany('api::san-pham.san-pham', {
            filters: { name: { $eqi: productData.name } },
            limit: 1,
          });

          if (existingProduct && existingProduct.length > 0) {
            errors.push(`Row ${i + 1}: Product "${productData.name}" already exists`);
            continue;
          }

          // Create the product
          await strapi.entityService.create('api::san-pham.san-pham', {
            data: productData,
          });

          imported++;
        } catch (error: any) {
          errors.push(`Row ${i + 1}: ${error.message}`);
        }
      }

      ctx.body = {
        success: true,
        imported,
        total: data.length,
        errors: errors.length > 0 ? errors : undefined,
        message: `Successfully imported ${imported} out of ${data.length} products`,
      };
    } catch (error: any) {
      console.error('Error importing products:', error);
      ctx.throw(500, `Failed to import products: ${error.message}`);
    }
  },

  async downloadSampleFile(ctx: any) {
    try {
      // Create sample data for Excel file
      const sampleData = [
        {
          'Tên sản phẩm': 'Sản phẩm mẫu 1',
          'Mô tả': 'Mô tả chi tiết sản phẩm mẫu 1',
          'Danh mục': 'Danh mục mẫu',
          'Thương hiệu': 'Thương hiệu mẫu',
          'Giá gốc': 100000,
          'Giá bán': 150000,
          'Tồn kho': 50,
          'Sản phẩm nổi bật': 'Không',
          'Trạng thái': 'Hoạt động',
        },
        {
          'Tên sản phẩm': 'Sản phẩm mẫu 2',
          'Mô tả': 'Mô tả chi tiết sản phẩm mẫu 2',
          'Danh mục': 'Danh mục mẫu',
          'Thương hiệu': 'Thương hiệu mẫu',
          'Giá gốc': 200000,
          'Giá bán': 280000,
          'Tồn kho': 30,
          'Sản phẩm nổi bật': 'Có',
          'Trạng thái': 'Hoạt động',
        },
      ];

      // Create workbook and worksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(sampleData);

      // Set column widths
      const columnWidths = [
        { wch: 25 }, // Tên sản phẩm
        { wch: 40 }, // Mô tả
        { wch: 20 }, // Danh mục
        { wch: 20 }, // Thương hiệu
        { wch: 15 }, // Giá gốc
        { wch: 15 }, // Giá bán
        { wch: 10 }, // Tồn kho
        { wch: 18 }, // Sản phẩm nổi bật
        { wch: 15 }, // Trạng thái
      ];
      worksheet['!cols'] = columnWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sản phẩm mẫu');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // Set response headers
      ctx.set({
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="sample_products.xlsx"',
        'Content-Length': buffer.length.toString(),
      });

      ctx.body = buffer;
    } catch (error: any) {
      console.error('Error creating sample file:', error);
      ctx.throw(500, `Failed to create sample file: ${error.message}`);
    }
  },
});

export default productController;
