import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Card,
  Tabs,
  Button,
  Space,
  Avatar,
  Typography,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Form,
  Input,
  Switch,
  message,
  Spin,
  Descriptions,
  Empty,
  Divider,
} from 'antd';
import {
  ArrowLeftOutlined,
  UserOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  TeamOutlined,
  CalendarOutlined,
  PhoneOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  image: string;
  phone: string;
  blocked: boolean;
  confirmed: boolean;
  createdAt: string;
  balance: number;
  referUser?: {
    id: number;
    name: string;
    username: string;
    phone: string;
    email: string;
  };
  role: {
    id: number;
    name: string;
    type: string;
  };
}

interface Order {
  id: number;
  code: string;
  statusOrder: string;
  priceAfterTax: number;
  createdAt: string;
}

interface Commission {
  id: number;
  amount: number;
  percentage: number;
  statusPaid: string;
  type: string;
  createdAt: string;
  order: {
    id: number;
    code: string;
  };
}

interface Withdrawal {
  id: number;
  amount: number;
  statusPaid: string;
  paidAt: string;
  createdAt: string;
}

interface UserDetailData {
  user: User;
  orders: Order[];
  commissions: Commission[];
  withdrawals: Withdrawal[];
  statistics: {
    totalOrders: number;
    totalCommission: number;
    pendingCommission: number;
    paidCommission: number;
  };
}

export const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { get, put } = useFetchClient();

  const [data, setData] = useState<UserDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [form] = Form.useForm();

  const fetchUserDetail = async (showLoading = false) => {
    if (!id) return;

    if (showLoading) {
      setLoading(true);
    }

    try {
      const response = await get<UserDetailData>(`/management/users/${id}`);
      setData(response.data);

      // Set form values
      if (response.data.user) {
        form.setFieldsValue({
          name: response.data.user.name,
          email: response.data.user.email,
          phone: response.data.user.phone,
          username: response.data.user.username,
          blocked: response.data.user.blocked,
        });
      }
    } catch (error) {
      console.error('Error fetching user detail:', error);
      message.error('Không thể tải thông tin đại lý');
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchUserDetail(true); // Show loading for initial fetch
  }, [id]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await put(`/management/users/${id}`, values);
      message.success('Cập nhật thông tin thành công');
      setEditing(false);
      // Fetch lại data để cập nhật giao diện (không show loading)
      await fetchUserDetail(false);
    } catch (error) {
      console.error('Error updating user:', error);
      message.error('Không thể cập nhật thông tin');
    }
  };

  const handleStatusChange = async (blocked: boolean) => {
    try {
      await put(`/management/users/${id}/status`, { blocked });
      message.success(`${blocked ? 'Khóa' : 'Mở khóa'} đại lý thành công`);
      // Fetch lại data để cập nhật giao diện (không show loading)
      await fetchUserDetail(false);
    } catch (error) {
      console.error('Error updating user status:', error);
      message.error('Không thể cập nhật trạng thái');
    }
  };

  if (loading) {
    return (
      <div
        className="order-detail-container"
        style={{
          minHeight: '100vh',
          background: '#f8fafc',
          padding: '24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Spin size="large" tip="Đang tải chi tiết đại lý..." />
      </div>
    );
  }

  if (!data) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text>Không tìm thấy thông tin đại lý</Text>
      </div>
    );
  }

  const { user, orders = [], commissions = [], withdrawals = [], statistics } = data;

  const orderColumns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'code',
      key: 'code',
      render: (code: string) => (
        <Text strong style={{ color: '#1e293b', fontSize: '14px' }}>
          #{code}
        </Text>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusOrder',
      key: 'statusOrder',
      render: (status: string) => {
        const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
          'Chờ xác nhận': { color: '#f59e0b', icon: <ClockCircleOutlined /> },
          'Chờ giao hàng': { color: '#3b82f6', icon: <ShoppingCartOutlined /> },
          'Đang giao hàng': { color: '#06b6d4', icon: <ShoppingCartOutlined /> },
          'Đã hoàn thành': { color: '#10b981', icon: <CheckCircleOutlined /> },
          'Đã hủy': { color: '#ef4444', icon: <StopOutlined /> },
        };
        const config = statusConfig[status] || { color: '#6b7280', icon: null };
        return (
          <Tag
            color={config.color}
            icon={config.icon}
            style={{
              margin: 0,
              padding: '6px 12px',
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              width: 'fit-content',
            }}
          >
            {status}
          </Tag>
        );
      },
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'priceAfterTax',
      key: 'priceAfterTax',
      render: (amount: number) => (
        <Text strong style={{ color: '#1e293b', fontSize: '14px' }}>
          {formatCurrency(amount)}
        </Text>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <Text style={{ color: '#64748b', fontSize: '13px' }}>
          {new Date(date).toLocaleDateString('vi-VN')}
        </Text>
      ),
    },
  ];

  const commissionColumns = [
    {
      title: 'Mã đơn hàng',
      key: 'orderCode',
      render: (record: Commission) => (
        <Text strong style={{ color: '#1e293b', fontSize: '14px' }}>
          #{record.order?.code}
        </Text>
      ),
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag
          color={type === 'referral' ? '#3b82f6' : '#10b981'}
          style={{
            margin: 0,
            padding: '6px 12px',
            borderRadius: 8,
            fontWeight: 500,
            fontSize: '13px',
          }}
        >
          {type === 'referral' ? 'Giới thiệu' : 'Công ty'}
        </Tag>
      ),
    },
    {
      title: 'Tỷ lệ',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage: number) => (
        <Text style={{ color: '#64748b', fontSize: '13px', fontWeight: 500 }}>{percentage}%</Text>
      ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <Text strong style={{ color: '#1e293b', fontSize: '14px' }}>
          {formatCurrency(amount)}
        </Text>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusPaid',
      key: 'statusPaid',
      render: (status: string) => {
        const statusConfig: Record<
          string,
          { color: string; icon: React.ReactNode; label: string }
        > = {
          'chờ duyệt': { color: '#f59e0b', icon: <ClockCircleOutlined />, label: 'Chờ duyệt' },
          'đã duyệt': { color: '#10b981', icon: <CheckCircleOutlined />, label: 'Đã duyệt' },
          'đã từ chối': { color: '#ef4444', icon: <StopOutlined />, label: 'Đã từ chối' },
        };
        const config = statusConfig[status] || { color: '#6b7280', icon: null, label: status };
        return (
          <Tag
            color={config.color}
            icon={config.icon}
            style={{
              margin: 0,
              padding: '6px 12px',
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              width: 'fit-content',
            }}
          >
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <Text style={{ color: '#64748b', fontSize: '13px' }}>
          {new Date(date).toLocaleDateString('vi-VN')}
        </Text>
      ),
    },
  ];

  const withdrawalColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (value: number) => (
        <Text style={{ color: '#64748b', fontSize: '13px', fontWeight: 500 }}>#{value}</Text>
      ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <Text strong style={{ color: '#1e293b', fontSize: '14px' }}>
          {formatCurrency(amount)}
        </Text>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusPaid',
      key: 'statusPaid',
      render: (status: string) => {
        const statusConfig: Record<
          string,
          { color: string; icon: React.ReactNode; label: string }
        > = {
          Pending: { color: '#f59e0b', icon: <ClockCircleOutlined />, label: 'Chờ duyệt' },
          Paid: { color: '#10b981', icon: <CheckCircleOutlined />, label: 'Đã thanh toán' },
          Canceled: { color: '#ef4444', icon: <StopOutlined />, label: 'Đã hủy' },
        };
        const config = statusConfig[status] || { color: '#6b7280', icon: null, label: status };
        return (
          <Tag
            color={config.color}
            icon={config.icon}
            style={{
              margin: 0,
              padding: '6px 12px',
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '13px',
              display: 'flex',
              alignItems: 'center',
              gap: 6,
              width: 'fit-content',
            }}
          >
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: 'Ngày yêu cầu',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <Text style={{ color: '#64748b', fontSize: '13px' }}>
          {new Date(date).toLocaleDateString('vi-VN')}
        </Text>
      ),
    },
    {
      title: 'Ngày thanh toán',
      dataIndex: 'paidAt',
      key: 'paidAt',
      render: (date: string, record: Withdrawal) => {
        if (record.statusPaid === 'Pending') {
          return (
            <Text style={{ color: '#94a3b8', fontSize: '13px', fontStyle: 'italic' }}>
              Chờ duyệt
            </Text>
          );
        }
        if (record.statusPaid === 'Canceled') {
          return (
            <Text style={{ color: '#94a3b8', fontSize: '13px', fontStyle: 'italic' }}>
              Đã hủy (tiền hoàn về ví)
            </Text>
          );
        }
        return (
          <Text style={{ color: '#64748b', fontSize: '13px' }}>
            {date ? new Date(date).toLocaleDateString('vi-VN') : '-'}
          </Text>
        );
      },
    },
  ];

  return (
    <div
      className="order-plugin order-detail-container"
      style={{
        minHeight: '100vh',
        background: '#f8fafc',
        padding: '24px',
      }}
    >
      {/* Back Button */}
      <Button
        icon={<ArrowLeftOutlined />}
        onClick={() => navigate('../users')}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: 12,
          borderRadius: 12,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '1px solid #e2e8f0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          marginBottom: 24,
          height: 48,
          padding: '0 20px',
          fontWeight: 500,
          color: '#1e293b',
        }}
      >
        Quay lại danh sách
      </Button>

      {/* User Header */}
      <div
        className="elegant-header"
        style={{
          borderRadius: 16,
          position: 'relative',
          overflow: 'hidden',
          marginBottom: 24,
        }}
      >
        <div
          className="elegant-user-header"
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 16,
            padding: '32px',
            border: '1px solid #e2e8f0',
            backdropFilter: 'blur(10px)',
            position: 'relative',
            zIndex: 2,
          }}
        >
          <Row align="middle" justify="space-between" gutter={[24, 24]}>
            {/* Left: User Info */}
            <Col xs={24} lg={16}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
                {/* Icon */}
                <img
                  src={user.image}
                  alt={user.name}
                  style={{ width: 64, height: 64, borderRadius: 16 }}
                />

                {/* User Details */}
                <div style={{ flex: 1 }}>
                  <Title
                    level={2}
                    style={{
                      margin: 0,
                      marginBottom: 12,
                      color: '#1e293b',
                      fontSize: 28,
                      fontWeight: 700,
                    }}
                  >
                    {user.name}
                  </Title>

                  {/* Status and Info Row */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 12 }}>
                    <div
                      style={{
                        padding: '8px 16px',
                        borderRadius: 8,
                        background: user.blocked ? '#ff4d4f' : '#52c41a',
                        color: 'white',
                        fontSize: 13,
                        fontWeight: 600,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 8,
                      }}
                    >
                      {user.blocked ? <StopOutlined /> : <CheckCircleOutlined />}
                      {user.blocked ? 'Đã khóa' : 'Hoạt động'}
                    </div>

                    <div
                      style={{
                        padding: '8px 12px',
                        borderRadius: 6,
                        background: '#f1f5f9',
                        color: '#475569',
                        fontSize: 13,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 6,
                      }}
                    >
                      <CalendarOutlined style={{ fontSize: 12 }} />
                      {new Date(user.createdAt).toLocaleDateString('vi-VN')}
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12, flexWrap: 'wrap' }}>
                    <div
                      style={{
                        padding: '6px 12px',
                        borderRadius: 6,
                        background: '#e0f2fe',
                        color: '#0369a1',
                        fontSize: 12,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <MailOutlined style={{ fontSize: 11 }} />
                      {user.email}
                    </div>

                    <div
                      style={{
                        padding: '6px 12px',
                        borderRadius: 6,
                        background: '#f0fdf4',
                        color: '#166534',
                        fontSize: 12,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <PhoneOutlined style={{ fontSize: 11 }} />
                      {user.phone}
                    </div>

                    <div
                      style={{
                        padding: '6px 12px',
                        borderRadius: 6,
                        background: '#fef3c7',
                        color: '#92400e',
                        fontSize: 12,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <TeamOutlined style={{ fontSize: 11 }} />
                      {user.role?.name || 'N/A'}
                    </div>

                    {user.confirmed && (
                      <div
                        style={{
                          padding: '6px 12px',
                          borderRadius: 6,
                          background: '#dcfce7',
                          color: '#166534',
                          fontSize: 12,
                          fontWeight: 500,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 4,
                        }}
                      >
                        <CheckCircleOutlined style={{ fontSize: 11 }} />
                        Đã xác thực
                      </div>
                    )}

                    {user.referUser && (
                      <div
                        style={{
                          padding: '6px 12px',
                          borderRadius: 6,
                          background: '#fef3c7',
                          color: '#92400e',
                          fontSize: 12,
                          fontWeight: 500,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 4,
                        }}
                      >
                        <UserOutlined style={{ fontSize: 11 }} />
                        Giới thiệu: {user.referUser.name}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Col>

            {/* Right: Actions */}
            <Col xs={24} lg={8}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 12,
                  alignItems: 'flex-end',
                }}
              >
                <Space>
                  <Switch
                    checked={!user.blocked}
                    onChange={(checked) => handleStatusChange(!checked)}
                    checkedChildren="Hoạt động"
                    unCheckedChildren="Khóa"
                    style={{ marginRight: 8 }}
                  />
                  {editing ? (
                    <Space>
                      <Button
                        icon={<SaveOutlined />}
                        type="primary"
                        onClick={handleSave}
                        style={{
                          borderRadius: 8,
                          background: '#059669',
                          borderColor: '#059669',
                        }}
                      >
                        Lưu
                      </Button>
                      <Button
                        icon={<CloseOutlined />}
                        onClick={() => setEditing(false)}
                        style={{ borderRadius: 8 }}
                      >
                        Hủy
                      </Button>
                    </Space>
                  ) : (
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => setEditing(true)}
                      style={{
                        borderRadius: 8,
                        background: '#1e293b',
                        borderColor: '#1e293b',
                        color: 'white',
                      }}
                    >
                      Chỉnh sửa
                    </Button>
                  )}
                </Space>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Số dư ví"
              value={user.balance || 0}
              prefix={<DollarOutlined style={{ color: '#059669' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#059669', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Tổng đơn hàng"
              value={statistics.totalOrders}
              prefix={<ShoppingCartOutlined style={{ color: '#3b82f6' }} />}
              valueStyle={{ color: '#1e293b', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Tổng hoa hồng"
              value={statistics.totalCommission}
              prefix={<DollarOutlined style={{ color: '#059669' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#1e293b', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Hoa hồng chờ duyệt"
              value={statistics.pendingCommission}
              prefix={<ClockCircleOutlined style={{ color: '#f59e0b' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#f59e0b', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Hoa hồng đã duyệt"
              value={statistics.paidCommission}
              prefix={<CheckCircleOutlined style={{ color: '#10b981' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#10b981', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6} md={4}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: '#ffffff',
            }}
          >
            <Statistic
              title="Hoa hồng đã rút"
              value={
                withdrawals
                  ?.filter((w) => w.statusPaid === 'Paid')
                  .reduce((sum, w) => sum + w.amount, 0) || 0
              }
              prefix={<DollarOutlined style={{ color: '#8b5cf6' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#8b5cf6', fontWeight: 600 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Tabs Section */}
      <Card
        style={{
          borderRadius: 12,
          border: '1px solid #e2e8f0',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          background: '#ffffff',
        }}
      >
        <Tabs
          defaultActiveKey="info"
          size="large"
          style={{
            fontFamily: "'Be Vietnam Pro', sans-serif",
          }}
          tabBarStyle={{
            marginBottom: 24,
            borderBottom: '2px solid #f0f0f0',
          }}
        >
          <TabPane tab="Thông tin cá nhân" key="info">
            <Form form={form} layout="vertical" disabled={!editing}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Họ và tên" name="name" rules={[{ required: true }]}>
                    <Input size="large" style={{ borderRadius: 8 }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Tên đăng nhập" name="username" rules={[{ required: true }]}>
                    <Input size="large" style={{ borderRadius: 8 }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Email" name="email" rules={[{ required: true, type: 'email' }]}>
                    <Input size="large" style={{ borderRadius: 8 }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Số điện thoại" name="phone" rules={[{ required: true }]}>
                    <Input size="large" style={{ borderRadius: 8 }} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Descriptions
                title="Thông tin hệ thống"
                bordered
                column={2}
                style={{
                  background: '#f8fafc',
                  borderRadius: 8,
                  padding: 16,
                }}
              >
                <Descriptions.Item label="ID">{user.id}</Descriptions.Item>
                <Descriptions.Item label="Vai trò">{user.role?.name}</Descriptions.Item>
                <Descriptions.Item label="Số dư ví">
                  <span style={{ color: '#059669', fontWeight: 600 }}>
                    {formatCurrency(user.balance || 0)}
                  </span>
                  <div style={{ fontSize: '12px', color: '#64748b', marginTop: 4 }}>
                    Số tiền có thể rút hiện tại
                  </div>
                </Descriptions.Item>

                <Descriptions.Item label="Người giới thiệu">
                  {user.referUser ? (
                    <div>
                      <div style={{ fontWeight: 500, color: '#1e293b' }}>{user.referUser.name}</div>
                      <div style={{ fontSize: '12px', color: '#64748b' }}>
                        {user.referUser.phone} • {user.referUser.email}
                      </div>
                    </div>
                  ) : (
                    <span style={{ color: '#94a3b8', fontStyle: 'italic' }}>Không có</span>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="Ngày tạo">
                  {new Date(user.createdAt).toLocaleString('vi-VN')}
                </Descriptions.Item>
                <Descriptions.Item label="Trạng thái xác thực">
                  {user.confirmed ? 'Đã xác thực' : 'Chưa xác thực'}
                </Descriptions.Item>
              </Descriptions>
            </Form>
          </TabPane>

          <TabPane tab={`Đơn hàng (${orders?.length || 0})`} key="orders">
            <Table
              columns={orderColumns}
              dataSource={orders}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={
                      <span
                        style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                      >
                        Không có đơn hàng
                      </span>
                    }
                  />
                ),
              }}
              style={{
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            />
          </TabPane>

          <TabPane tab={`Hoa hồng (${commissions?.length || 0})`} key="commissions">
            <Table
              columns={commissionColumns}
              dataSource={commissions}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={
                      <span
                        style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                      >
                        Không có hoa hồng
                      </span>
                    }
                  />
                ),
              }}
              style={{
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            />
          </TabPane>

          <TabPane tab={`Lịch sử rút tiền (${withdrawals?.length || 0})`} key="withdrawals">
            <Table
              columns={withdrawalColumns}
              dataSource={withdrawals}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              locale={{
                emptyText: (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={
                      <span
                        style={{ color: '#64748b', fontFamily: "'Be Vietnam Pro', sans-serif" }}
                      >
                        Không có lịch sử rút tiền
                      </span>
                    }
                  />
                ),
              }}
              style={{
                fontFamily: "'Be Vietnam Pro', sans-serif",
              }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};
