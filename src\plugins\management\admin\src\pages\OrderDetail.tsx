// @ts-ignore
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
// @ts-ignore
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Button,
  Space,
  Divider,
  Table,
  Spin,
  message,
  Image,
  Empty,
  Select,
  Modal,
  Tooltip,
  Tabs,
  Input,
  InputNumber,
  Form,
} from 'antd';
// @ts-ignore
import {
  ArrowLeftOutlined,
  ShoppingCartOutlined,
  CalendarOutlined,
  DollarOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  ExclamationCircleOutlined,
  PrinterOutlined,
  EditOutlined,
  SaveOutlined,
  ReloadOutlined,
  HistoryOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  CreditCardOutlined,
  TagOutlined,
  FileTextOutlined,
  CarOutlined,
  GiftOutlined,
  StopOutlined,
  PercentageOutlined,
} from '@ant-design/icons';
import './OrderDetail.css';
import { useFetchClient } from '@strapi/strapi/admin';
import { useAddressData } from '../hooks/useAddressData';

const { Title, Text } = Typography;

// Component thanh nối với hiệu ứng loading
const LoadingConnector = ({
  isActive,
  isCompleted,
  color,
}: {
  isActive: boolean;
  isCompleted: boolean;
  color: string;
}) => {
  const [loadingWidth, setLoadingWidth] = React.useState(0);

  React.useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setLoadingWidth((prev) => {
        if (prev >= 100) {
          return 0; // Reset về 0
        }
        return prev + 1; // Tăng 1% mỗi lần
      });
    }, 30); // Cập nhật mỗi 30ms = 3 giây để hoàn thành

    return () => clearInterval(interval);
  }, [isActive]);

  return (
    <div
      style={{
        width: '100%',
        height: '4px',
        background: '#e2e8f0',
        borderRadius: '2px',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Thanh completed */}
      {isCompleted && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: '#10b981',
            borderRadius: '2px',
          }}
        />
      )}

      {/* Thanh loading cho active step */}
      {isActive && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: `${loadingWidth}%`,
            height: '100%',
            background: `linear-gradient(90deg, ${color}, ${color}dd)`,
            borderRadius: '2px',
            boxShadow: `0 0 8px ${color}`,
            transition: 'width 0.03s linear',
          }}
        />
      )}
    </div>
  );
};

// Component step tùy chỉnh với layout grid
const CustomStep = ({
  icon,
  title,
  description,
  isActive,
  isCompleted,
  color,
}: {
  icon: React.ReactElement;
  title: string;
  description: string;
  isActive: boolean;
  isCompleted: boolean;
  color: string;
}) => {
  const [scale, setScale] = React.useState(1);

  React.useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setScale((prev) => (prev === 1.1 ? 1.2 : 1.1));
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', flex: 1 }}>
      {/* Icon */}
      <div
        style={{
          width: 48,
          height: 48,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: isCompleted
            ? '#10b981'
            : isActive
              ? `linear-gradient(45deg, ${color}, ${color}dd)`
              : '#f1f5f9',
          border: isCompleted
            ? '3px solid #10b981'
            : isActive
              ? `3px solid ${color}`
              : '3px solid #e2e8f0',
          transform: `scale(${isActive ? scale : 1})`,
          transition: 'all 0.3s ease',
          boxShadow: isActive
            ? `0 0 20px ${color}40, 0 0 40px ${color}20`
            : isCompleted
              ? '0 0 10px rgba(16, 185, 129, 0.3)'
              : 'none',
          position: 'relative',
          zIndex: 2,
        }}
      >
        {React.cloneElement(icon, {
          style: {
            fontSize: 20,
            color: isCompleted || isActive ? 'white' : '#64748b',
          },
        } as any)}

        {/* Rotating ring for active step */}
        {isActive && (
          <div
            style={{
              position: 'absolute',
              top: -6,
              left: -6,
              right: -6,
              bottom: -6,
              borderRadius: '50%',
              border: `3px solid ${color}`,
              borderTopColor: 'transparent',
              animation: 'spin 2s linear infinite',
            }}
          />
        )}
      </div>

      {/* Text */}
      <div style={{ textAlign: 'center', marginTop: 16, zIndex: 2 }}>
        <div
          style={{
            fontWeight: isActive ? 700 : 600,
            fontSize: isActive ? 16 : 14,
            color: isActive ? color : '#1e293b',
            marginBottom: 6,
            textShadow: isActive ? `0 0 8px ${color}40` : 'none',
          }}
        >
          {title}
        </div>
        <div
          style={{
            fontSize: 12,
            color: isActive ? '#475569' : '#64748b',
            opacity: isActive ? 1 : 0.8,
            lineHeight: 1.4,
          }}
        >
          {description}
        </div>
      </div>
    </div>
  );
};

const statusOptions = [
  { value: 'Chờ xác nhận', label: 'Chờ xác nhận', color: '#faad14', icon: <FileTextOutlined /> },
  {
    value: 'Chờ giao hàng',
    label: 'Chờ giao hàng',
    color: '#1890ff',
    icon: <ClockCircleOutlined />,
  },
  { value: 'Đang giao hàng', label: 'Đang giao hàng', color: '#722ed1', icon: <TruckOutlined /> },
  {
    value: 'Đã hoàn thành',
    label: 'Đã hoàn thành',
    color: '#52c41a',
    icon: <CheckCircleOutlined />,
  },
  { value: 'Đã hủy', label: 'Đã hủy', color: '#ff4d4f', icon: <ExclamationCircleOutlined /> },
];

// Helper function to get status config
const getStatusConfig = (status: string) => {
  return statusOptions.find((opt) => opt.value === status) || statusOptions[0];
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

// Helper function to check if customer info can be edited based on order status
const canEditCustomerInfo = (status: string) => {
  // Chỉ cho phép sửa thông tin khách hàng khi đơn hàng ở trạng thái:
  // - Chờ xác nhận: Có thể sửa vì chưa bắt đầu xử lý
  // - Chờ giao hàng: Có thể sửa địa chỉ giao hàng nếu cần
  const editableStatuses = ['Chờ xác nhận', 'Chờ giao hàng'];
  return editableStatuses.includes(status);
};

const OrderDetail: React.FC = () => {
  const { id: orderId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  // Customer edit states
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [updatingCustomer, setUpdatingCustomer] = useState(false);
  const [customerForm] = Form.useForm();

  // Address data hook
  const {
    provinces,
    districts,
    wards,
    updateDistricts,
    updateWards,
    getProvinceCodeByName,
    getDistrictCodeByName,
    getWardCodeByName,
  } = useAddressData();

  // Address selection states
  const [selectedProvinceCode, setSelectedProvinceCode] = useState<string>('');
  const [selectedDistrictCode, setSelectedDistrictCode] = useState<string>('');
  const [selectedWardCode, setSelectedWardCode] = useState<string>('');

  // Commission edit states
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [selectedCommission, setSelectedCommission] = useState<any>(null);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [calculatedAmount, setCalculatedAmount] = useState<number>(0);
  const [editForm] = Form.useForm();

  const { get, put } = useFetchClient();

  const fetchOrderDetail = async () => {
    setLoading(true);
    try {
      const { data } = await get(`/order/orders/${orderId}`);
      setOrder(data);
    } catch (error) {
      console.error('Error fetching order detail:', error);
      message.error('Không thể tải chi tiết đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    setUpdatingStatus(true);
    try {
      await put(`/order/orders/${orderId}/status`, { status: newStatus });
      message.success('Cập nhật trạng thái đơn hàng thành công');
      setOrder({ ...order, statusOrder: newStatus });
      setShowStatusModal(false);
    } catch (error) {
      console.error('Error updating order status:', error);
      message.error('Không thể cập nhật trạng thái đơn hàng');
    } finally {
      setUpdatingStatus(false);
    }
  };

  const openStatusModal = () => {
    const availableOptions = getAvailableStatusOptions(order.statusOrder);

    if (availableOptions.length === 0) {
      message.info('Đơn hàng này không thể thay đổi trạng thái');
      return;
    }

    setSelectedStatus(availableOptions[0].value); // Set first available option as default
    setShowStatusModal(true);
  };

  // Get available status options (only forward transitions + cancel)
  const getAvailableStatusOptions = (currentStatus: string) => {
    const statusOrder = ['Chờ xác nhận', 'Chờ giao hàng', 'Đang giao hàng', 'Hoàn thành'];
    const currentIndex = statusOrder.indexOf(currentStatus);

    // If current status is "Đã hủy", no transitions allowed
    if (currentStatus === 'Đã hủy') {
      return [];
    }

    // If current status is "Hoàn thành", only allow cancel
    if (currentStatus === 'Hoàn thành') {
      return statusOptions.filter((option) => option.value === 'Đã hủy');
    }

    // For other statuses, allow forward transitions + cancel
    const availableOptions = [];

    // Add forward transitions
    for (let i = currentIndex + 1; i < statusOrder.length; i++) {
      const option = statusOptions.find((opt) => opt.value === statusOrder[i]);
      if (option) availableOptions.push(option);
    }

    // Always allow cancel (except if already cancelled or completed)
    if (currentStatus !== 'Đã hủy' && currentStatus !== 'Hoàn thành') {
      const cancelOption = statusOptions.find((opt) => opt.value === 'Đã hủy');
      if (cancelOption) availableOptions.push(cancelOption);
    }

    return availableOptions;
  };

  // Customer update functions
  const openCustomerModal = () => {
    const customer = order.customer;

    // Initialize address data if customer has existing address
    if (customer?.city) {
      const provinceCode = getProvinceCodeByName(customer.city);
      if (provinceCode) {
        setSelectedProvinceCode(provinceCode);
        updateDistricts(provinceCode);

        if (customer?.district) {
          const districtCode = getDistrictCodeByName(customer.district, provinceCode);
          if (districtCode) {
            setSelectedDistrictCode(districtCode);
            updateWards(districtCode);

            if (customer?.ward) {
              const wardCode = getWardCodeByName(customer.ward, districtCode);
              if (wardCode) {
                setSelectedWardCode(wardCode);
              }
            }
          }
        }
      }
    }

    customerForm.setFieldsValue({
      name: customer?.name || '',
      email: customer?.email || '',
      phone: customer?.phone || '',
      address: customer?.address || '',
      provinceCode: getProvinceCodeByName(customer?.city || ''),
      districtCode: getDistrictCodeByName(
        customer?.district || '',
        getProvinceCodeByName(customer?.city || '')
      ),
      wardCode: getWardCodeByName(
        customer?.ward || '',
        getDistrictCodeByName(customer?.district || '', getProvinceCodeByName(customer?.city || ''))
      ),
    });
    setShowCustomerModal(true);
  };

  const handleCustomerUpdate = async (values: any) => {
    setUpdatingCustomer(true);
    try {
      // Convert codes back to names for storage
      const customerData = {
        name: values.name,
        email: values.email,
        phone: values.phone,
        address: values.address,
        city: provinces.find((p) => p.code === values.provinceCode)?.name_with_type || '',
        district: districts.find((d) => d.code === values.districtCode)?.name_with_type || '',
        ward: wards.find((w) => w.code === values.wardCode)?.name_with_type || '',
      };

      await put(`/order/orders/${orderId}/customer`, {
        customer: customerData,
      });
      message.success('Cập nhật thông tin khách hàng thành công');
      setOrder({
        ...order,
        customer: {
          ...order.customer,
          ...customerData,
        },
      });
      setShowCustomerModal(false);
    } catch (error) {
      console.error('Error updating customer info:', error);
      message.error('Không thể cập nhật thông tin khách hàng');
    } finally {
      setUpdatingCustomer(false);
    }
  };

  // Address change handlers
  const handleProvinceChange = (provinceCode: string) => {
    setSelectedProvinceCode(provinceCode);
    setSelectedDistrictCode('');
    setSelectedWardCode('');
    updateDistricts(provinceCode);
    customerForm.setFieldsValue({
      districtCode: undefined,
      wardCode: undefined,
    });
  };

  const handleDistrictChange = (districtCode: string) => {
    setSelectedDistrictCode(districtCode);
    setSelectedWardCode('');
    updateWards(districtCode);
    customerForm.setFieldsValue({
      wardCode: undefined,
    });
  };

  const handleWardChange = (wardCode: string) => {
    setSelectedWardCode(wardCode);
  };

  // Commission handlers
  const handleEditCommission = (commission: any) => {
    setSelectedCommission(commission);
    const orderTotal = order.priceAfterTax || 0;
    const initialAmount = Math.round((orderTotal * commission.percentage) / 100);
    setCalculatedAmount(initialAmount);

    editForm.setFieldsValue({
      percentage: commission.percentage,
      reason: '',
    });
    setEditModalVisible(true);
  };

  // Calculate amount when percentage changes
  const handlePercentageChange = (value: number | null) => {
    if (value && order.priceAfterTax) {
      const newAmount = Math.round((order.priceAfterTax * value) / 100);
      setCalculatedAmount(newAmount);
    } else {
      setCalculatedAmount(0);
    }
  };

  const handleViewHistory = async (commission: any) => {
    setSelectedCommission(commission);
    try {
      const response = await get(`/order/commission/${commission.id}/audit-logs`);
      setAuditLogs(response.data.data || []);
      setHistoryModalVisible(true);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      // Show empty history instead of error if audit table doesn't exist
      setAuditLogs([]);
      setHistoryModalVisible(true);
      message.warning(
        'Lịch sử thay đổi chưa khả dụng. Vui lòng restart server để kích hoạt tính năng này.'
      );
    }
  };

  const handleCommissionUpdate = async (values: any) => {
    try {
      await put(`/order/commission/${selectedCommission.id}`, values);
      message.success('Cập nhật hoa hồng thành công');
      setEditModalVisible(false);
      // Refresh order data
      fetchOrderDetail();
    } catch (error) {
      console.error('Error updating commission:', error);
      message.error('Không thể cập nhật hoa hồng');
    }
  };

  // Print invoice handler
  const handlePrintInvoice = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      message.error('Không thể mở cửa sổ in. Vui lòng cho phép popup.');
      return;
    }

    const invoiceHTML = generateInvoiceHTML();
    printWindow.document.write(invoiceHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };

  // Generate invoice HTML
  const generateInvoiceHTML = () => {
    const currentDate = new Date().toLocaleDateString('vi-VN');
    const totalAmount = order.priceAfterTax || 0;
    const taxAmount = order.taxAmount || 0;
    const shippingAmount = order.shippingAmount || 0;
    const discountAmount = order.discountAmount || 0;
    const subtotal = totalAmount - taxAmount - shippingAmount + discountAmount;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Hóa đơn - ${order.code}</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            padding: 20px;
          }
          .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
          }
          .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
          }
          .invoice-title {
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0;
            text-transform: uppercase;
          }
          .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
          }
          .customer-info, .order-info {
            width: 48%;
          }
          .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            text-decoration: underline;
          }
          .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
          }
          .products-table th, .products-table td {
            border: 1px solid #333;
            padding: 10px;
            text-align: left;
          }
          .products-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
          }
          .text-right { text-align: right; }
          .text-center { text-align: center; }
          .total-section {
            margin-top: 20px;
            border-top: 2px solid #333;
            padding-top: 15px;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }
          .total-final {
            font-weight: bold;
            font-size: 18px;
            border-top: 1px solid #333;
            padding-top: 10px;
            margin-top: 10px;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            border-top: 1px solid #ccc;
            padding-top: 20px;
          }
          @media print {
            body { padding: 0; }
            .invoice-container { padding: 20px; }
          }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          <div class="header">
            <div class="company-name">CÔNG TY TNHH ABC</div>
            <div>Địa chỉ: 123 Đường ABC, Quận XYZ, TP.HCM</div>
            <div>Điện thoại: 0123.456.789 | Email: <EMAIL></div>
            <div class="invoice-title">HÓA ĐƠN BÁN HÀNG</div>
          </div>

          <div class="invoice-info">
            <div class="customer-info">
              <div class="info-title">THÔNG TIN KHÁCH HÀNG:</div>
              <div><strong>Họ tên:</strong> ${order.customer?.name || 'N/A'}</div>
              <div><strong>Điện thoại:</strong> ${order.customer?.phone || 'N/A'}</div>
              <div><strong>Email:</strong> ${order.customer?.email || 'N/A'}</div>
              <div><strong>Địa chỉ:</strong> ${order.customer?.address || 'N/A'}</div>
              <div>${order.customer?.ward || ''} ${order.customer?.district || ''} ${order.customer?.city || ''}</div>
            </div>
            <div class="order-info">
              <div class="info-title">THÔNG TIN ĐƠN HÀNG:</div>
              <div><strong>Mã đơn hàng:</strong> ${order.code}</div>
              <div><strong>Ngày tạo:</strong> ${new Date(order.createdAt).toLocaleDateString('vi-VN')}</div>
              <div><strong>Ngày in:</strong> ${currentDate}</div>
              <div><strong>Trạng thái:</strong> ${order.statusOrder}</div>
              <div><strong>Thanh toán:</strong> ${order.paymentStatus ? 'Đã thanh toán' : 'Chưa thanh toán'}</div>
            </div>
          </div>

          <table class="products-table">
            <thead>
              <tr>
                <th style="width: 5%">STT</th>
                <th style="width: 40%">Tên sản phẩm</th>
                <th style="width: 15%">Đơn giá</th>
                <th style="width: 10%">Số lượng</th>
                <th style="width: 15%">Giảm giá</th>
                <th style="width: 15%">Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${
                order.products
                  ?.map((product: any, index: number) => {
                    const total = (product.price || 0) * (product.quantity || 1);
                    const discount = product.discount || 0;
                    const finalPrice = total - discount;
                    return `
                  <tr>
                    <td class="text-center">${index + 1}</td>
                    <td>${product.name || 'N/A'}</td>
                    <td class="text-right">${formatCurrency(product.price || 0)}</td>
                    <td class="text-center">${product.quantity || 1}</td>
                    <td class="text-right">${formatCurrency(discount)}</td>
                    <td class="text-right">${formatCurrency(finalPrice)}</td>
                  </tr>
                `;
                  })
                  .join('') || '<tr><td colspan="6" class="text-center">Không có sản phẩm</td></tr>'
              }
            </tbody>
          </table>

          <div class="total-section">
            <div class="total-row">
              <span>Tạm tính:</span>
              <span>${formatCurrency(subtotal)}</span>
            </div>
            ${
              discountAmount > 0
                ? `
            <div class="total-row">
              <span>Giảm giá:</span>
              <span>-${formatCurrency(discountAmount)}</span>
            </div>
            `
                : ''
            }
            ${
              shippingAmount > 0
                ? `
            <div class="total-row">
              <span>Phí vận chuyển:</span>
              <span>${formatCurrency(shippingAmount)}</span>
            </div>
            `
                : ''
            }
            ${
              taxAmount > 0
                ? `
            <div class="total-row">
              <span>Thuế VAT:</span>
              <span>${formatCurrency(taxAmount)}</span>
            </div>
            `
                : ''
            }
            <div class="total-row total-final">
              <span>TỔNG CỘNG:</span>
              <span>${formatCurrency(totalAmount)}</span>
            </div>
          </div>

          <div class="footer">
            <div style="margin-bottom: 20px;">
              <strong>Cảm ơn quý khách đã mua hàng!</strong>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: 40px;">
              <div style="text-align: center;">
                <div><strong>Người mua hàng</strong></div>
                <div style="margin-top: 60px;">(Ký, ghi rõ họ tên)</div>
              </div>
              <div style="text-align: center;">
                <div><strong>Người bán hàng</strong></div>
                <div style="margin-top: 60px;">(Ký, ghi rõ họ tên)</div>
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  const getOrderSteps = (currentStatus: string) => {
    const steps = [
      {
        title: 'Chờ duyệt',
        description: 'Đơn hàng đang chờ xác nhận',
        icon: <FileTextOutlined />,
        status: 'Chờ xác nhận',
        color: '#f59e0b',
      },
      {
        title: 'Chờ giao hàng',
        description: 'Đơn hàng đã được xác nhận, chuẩn bị giao',
        icon: <ClockCircleOutlined />,
        status: 'Chờ giao hàng',
        color: '#3b82f6',
      },
      {
        title: 'Đang giao hàng',
        description: 'Đơn hàng đang được vận chuyển',
        icon: <CarOutlined />,
        status: 'Đang giao hàng',
        color: '#8b5cf6',
      },
      {
        title: 'Hoàn thành',
        description: 'Đơn hàng đã được giao thành công',
        icon: <GiftOutlined />,
        status: 'Đã hoàn thành',
        color: '#10b981',
      },
    ];

    // Xử lý trường hợp đơn hàng bị hủy
    if (currentStatus === 'Đã hủy') {
      return [
        {
          title: 'Đã hủy',
          description: 'Đơn hàng đã bị hủy',
          icon: <StopOutlined />,
          stepStatus: 'error',
          status: 'Đã hủy',
          color: '#ef4444',
          isActive: true,
        },
      ];
    }

    // Tìm current step index dựa trên trạng thái hiện tại
    let currentStepIndex = 0;
    switch (currentStatus) {
      case 'Chờ xác nhận':
        currentStepIndex = 0;
        break;
      case 'Chờ giao hàng':
        currentStepIndex = 1;
        break;
      case 'Đang giao hàng':
        currentStepIndex = 2;
        break;
      case 'Đã hoàn thành':
        currentStepIndex = 3;
        break;
      default:
        currentStepIndex = 0;
    }

    return steps.map((step, index) => ({
      ...step,
      stepStatus:
        index < currentStepIndex ? 'finish' : index === currentStepIndex ? 'process' : 'wait',
      isActive: index === currentStepIndex,
    }));
  };

  useEffect(() => {
    if (orderId) {
      fetchOrderDetail();
    } else {
      // Redirect to list if no orderId
      navigate(-1);
    }
  }, [orderId, navigate]);

  if (loading) {
    return (
      <div
        className="order-detail-container"
        style={{
          minHeight: '100vh',
          background: '#f8fafc',
          padding: '24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Spin size="large" tip="Đang tải chi tiết đơn hàng..." />
      </div>
    );
  }

  if (!order) {
    return (
      <div
        className="order-detail-container"
        style={{
          minHeight: '100vh',
          background: '#f8fafc',
          padding: '24px',
        }}
      >
        <Empty description="Không tìm thấy đơn hàng" />
      </div>
    );
  }

  const statusConfig = getStatusConfig(order.statusOrder);

  const productColumns = [
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          {record.image && record.image.length > 0 && (
            <Image
              width={50}
              height={50}
              src={record.image[0].url}
              style={{ borderRadius: 8 }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          )}
          <div>
            <div style={{ fontWeight: 600, color: '#1e293b' }}>{text}</div>
            <div style={{ fontSize: '12px', color: '#64748b' }}>Mã: {record.id || 'N/A'}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number) => (
        <div style={{ textAlign: 'center', fontWeight: 600 }}>{quantity || 1}</div>
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      width: 150,
      render: (price: number) => (
        <div style={{ fontWeight: 600, color: '#059669' }}>{formatCurrency(price || 0)}</div>
      ),
    },
    {
      title: 'Thành tiền',
      key: 'total',
      width: 150,
      render: (_: any, record: any) => {
        const total = (record.price || 0) * (record.quantity || 1);
        return (
          <div style={{ fontWeight: 700, color: '#1e293b', fontSize: '16px' }}>
            {formatCurrency(total)}
          </div>
        );
      },
    },
  ];

  return (
    <div
      className="order-detail-container"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%)',
        padding: '20px',
      }}
    >
      <Button
        type="text"
        icon={<ArrowLeftOutlined />}
        onClick={() => navigate(-1)}
        className="elegant-back-button"
        style={{
          padding: '16px 32px',
          height: 'auto',
          color: '#1e293b',
          fontSize: 16,
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 12,
          borderRadius: 12,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '1px solid #e2e8f0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          marginBottom: 24,
        }}
      >
        Quay lại danh sách
      </Button>

      {/* Minimalist Header */}
      <div
        className="elegant-header"
        style={{
          borderRadius: 16,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Custom Order Progress Steps */}
        <div style={{ position: 'relative', zIndex: 2, marginBottom: 20 }}>
          <div
            className="elegant-progress-container"
            style={{
              background: '#f8fafc',
              borderRadius: 12,
              padding: '32px',
              border: '1px solid #e2e8f0',
            }}
          >
            <div style={{ position: 'relative' }}>
              {/* Steps */}
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                  width: '100%',
                }}
              >
                {getOrderSteps(order.statusOrder).map((step) => (
                  <CustomStep
                    key={step.status}
                    icon={step.icon}
                    title={step.title}
                    description={step.description}
                    isActive={step.stepStatus === 'process'}
                    isCompleted={step.stepStatus === 'finish'}
                    color={step.color || '#64748b'}
                  />
                ))}
              </div>

              {/* Connection Lines */}
              <div
                style={{
                  position: 'absolute',
                  top: '24px',
                  left: '12%',
                  right: '12%',
                  height: '4px',
                  display: 'flex',
                  zIndex: 1,
                }}
              >
                {getOrderSteps(order.statusOrder).map((step, index) => {
                  const steps = getOrderSteps(order.statusOrder);
                  if (index === steps.length - 1) return null; // Không có line cho step cuối

                  return (
                    <LoadingConnector
                      key={`line-${index}`}
                      isActive={step.stepStatus === 'process'}
                      isCompleted={step.stepStatus === 'finish'}
                      color={step.color || '#64748b'}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Minimalist Order Header */}
        <div
          className="elegant-order-header"
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 16,
            padding: '32px',
            border: '1px solid #e2e8f0',
            backdropFilter: 'blur(10px)',
            position: 'relative',
            zIndex: 2,
          }}
        >
          <Row align="middle" justify="space-between" gutter={[24, 24]}>
            {/* Left: Order Info */}
            <Col xs={24} lg={16}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
                {/* Icon */}
                <div
                  style={{
                    width: 64,
                    height: 64,
                    borderRadius: 16,
                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: 24,
                    boxShadow: '0 8px 24px rgba(59, 130, 246, 0.3)',
                  }}
                >
                  <ShoppingCartOutlined />
                </div>

                {/* Order Details */}
                <div style={{ flex: 1 }}>
                  <Title
                    level={2}
                    style={{
                      margin: 0,
                      marginBottom: 12,
                      color: '#1e293b',
                      fontSize: 28,
                      fontWeight: 700,
                    }}
                  >
                    #{order.code}
                  </Title>

                  {/* Status and Date Row */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 12 }}>
                    <Tooltip title="Nhấn để thay đổi trạng thái">
                      <div
                        onClick={openStatusModal}
                        style={{
                          padding: '8px 16px',
                          borderRadius: 8,
                          background: statusConfig.color,
                          color: 'white',
                          fontSize: 13,
                          fontWeight: 600,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 8,
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-1px)';
                          e.currentTarget.style.boxShadow = `0 4px 12px ${statusConfig.color}40`;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        {statusConfig.icon}
                        {statusConfig.label}
                        <EditOutlined style={{ fontSize: 11 }} />
                      </div>
                    </Tooltip>

                    <div
                      style={{
                        padding: '8px 12px',
                        borderRadius: 6,
                        background: '#f1f5f9',
                        color: '#475569',
                        fontSize: 13,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 6,
                      }}
                    >
                      <CalendarOutlined style={{ fontSize: 12 }} />
                      {new Date(order.createdAt).toLocaleDateString('vi-VN')}
                    </div>
                  </div>

                  {/* Payment and Product Info */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <div
                      style={{
                        padding: '6px 12px',
                        borderRadius: 6,
                        background: order.paymentStatus ? '#dcfce7' : '#fef3c7',
                        color: order.paymentStatus ? '#166534' : '#92400e',
                        fontSize: 12,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <CreditCardOutlined style={{ fontSize: 11 }} />
                      {order.paymentStatus ? 'Đã thanh toán' : 'Chưa thanh toán'}
                    </div>
                    <div
                      style={{
                        padding: '6px 12px',
                        borderRadius: 6,
                        background: '#e0e7ff',
                        color: '#3730a3',
                        fontSize: 12,
                        fontWeight: 500,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <TagOutlined style={{ fontSize: 11 }} />
                      {order.products?.length || 0} sản phẩm
                    </div>
                  </div>
                </div>
              </div>
            </Col>

            {/* Right: Price and Actions */}
            <Col xs={24} lg={8}>
              <div style={{ textAlign: 'right' }}>
                {/* Price Display */}
                <div
                  style={{
                    background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
                    borderRadius: 12,
                    padding: '20px',
                    marginBottom: 16,
                    border: '1px solid #bbf7d0',
                  }}
                >
                  <div style={{ fontSize: 14, color: '#166534', marginBottom: 8 }}>
                    Tổng giá trị
                  </div>
                  <div
                    style={{
                      fontSize: 24,
                      fontWeight: 700,
                      color: '#15803d',
                      lineHeight: 1,
                    }}
                  >
                    {formatCurrency(order.priceAfterTax)}
                  </div>
                </div>

                {/* Action Buttons */}
                <Space size="small" wrap>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={openStatusModal}
                    style={{
                      background: '#3b82f6',
                      borderColor: '#3b82f6',
                      borderRadius: 8,
                      fontWeight: 600,
                      fontSize: 13,
                    }}
                  >
                    Cập nhật
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={fetchOrderDetail}
                    style={{
                      borderRadius: 8,
                      fontSize: 13,
                    }}
                  >
                    Làm mới
                  </Button>
                  <Button
                    icon={<PrinterOutlined />}
                    onClick={handlePrintInvoice}
                    style={{
                      borderRadius: 8,
                      fontSize: 13,
                    }}
                  >
                    In
                  </Button>
                </Space>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* Minimalist Main Content Tabs */}
      <div
        className="elegant-tabs-container"
        style={{
          borderRadius: 16,
          marginBottom: 32,
        }}
      >
        <Tabs
          defaultActiveKey="1"
          size="large"
          className="elegant-tabs"
          items={[
            {
              key: '1',
              label: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <UserOutlined style={{ fontSize: 16 }} />
                  <span style={{ fontWeight: 600, fontSize: 16 }}>Thông tin chung</span>
                </div>
              ),
              children: (
                <Row gutter={[32, 32]}>
                  {/* Customer Information */}
                  <Col xs={24} lg={12} style={{ paddingRight: 10 }}>
                    <Card
                      title={
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                            <div
                              style={{
                                width: 30,
                                height: 30,
                                borderRadius: 8,
                                background: '#6366f1',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                              }}
                            >
                              <UserOutlined style={{ fontSize: 18 }} />
                            </div>
                            <span style={{ fontWeight: 600, color: '#1e293b', fontSize: 18 }}>
                              Thông tin khách hàng
                            </span>
                          </div>
                          <Tooltip
                            title={
                              canEditCustomerInfo(order.statusOrder)
                                ? 'Chỉnh sửa thông tin khách hàng'
                                : 'Không thể sửa thông tin khách hàng ở trạng thái này'
                            }
                          >
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              size="small"
                              onClick={openCustomerModal}
                              disabled={!canEditCustomerInfo(order.statusOrder)}
                              style={{
                                color: canEditCustomerInfo(order.statusOrder)
                                  ? '#6366f1'
                                  : '#94a3b8',
                                fontSize: 14,
                                padding: '4px 8px',
                                height: 'auto',
                              }}
                            />
                          </Tooltip>
                        </div>
                      }
                      className="order-card customer-card"
                      style={{
                        borderRadius: 12,
                        border: 'none',
                        overflow: 'hidden',
                        height: '100%',
                      }}
                    >
                      <div style={{ padding: '8px 0' }}>
                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <UserOutlined style={{ color: '#8b5cf6', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Tên khách hàng
                            </Text>
                          </div>
                          <Text strong style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            {order.customer?.name || 'Chưa có thông tin'}
                          </Text>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <MailOutlined style={{ color: '#8b5cf6', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Email
                            </Text>
                          </div>
                          <Text style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            {order.customer?.email || 'Chưa có thông tin'}
                          </Text>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <PhoneOutlined style={{ color: '#8b5cf6', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Số điện thoại
                            </Text>
                          </div>
                          <Text style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            {order.customer?.phone || 'Chưa có thông tin'}
                          </Text>
                        </div>

                        <div>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <EnvironmentOutlined style={{ color: '#8b5cf6', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Địa chỉ giao hàng
                            </Text>
                          </div>
                          <Text
                            style={{
                              color: '#1e293b',
                              fontSize: 16,
                              marginLeft: 28,
                              lineHeight: 1.5,
                            }}
                          >
                            {order.customer?.address &&
                            order.customer?.ward &&
                            order.customer?.district &&
                            order.customer?.city
                              ? `${order.customer.address}, ${order.customer.ward}, ${order.customer.district}, ${order.customer.city}`
                              : 'Chưa có thông tin địa chỉ'}
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>

                  {/* Order Information */}
                  <Col xs={24} lg={12} style={{ paddingLeft: 10 }}>
                    <Card
                      title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                          <div
                            style={{
                              width: 30,
                              height: 30,
                              borderRadius: 8,
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                            }}
                          >
                            <CalendarOutlined style={{ fontSize: 20 }} />
                          </div>
                          <span style={{ fontWeight: 700, color: '#1e293b', fontSize: 18 }}>
                            Thông tin đơn hàng
                          </span>
                        </div>
                      }
                      className="order-card order-info-card"
                      style={{
                        borderRadius: 12,
                        border: 'none',
                        overflow: 'hidden',
                        height: '100%',
                      }}
                    >
                      <div style={{ padding: '8px 0' }}>
                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <TagOutlined style={{ color: '#667eea', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Mã đơn hàng
                            </Text>
                          </div>
                          <Text strong style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            #{order.code}
                          </Text>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            {statusConfig.icon &&
                              React.cloneElement(statusConfig.icon, {
                                style: { color: statusConfig.color, fontSize: 16 },
                              })}
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Trạng thái đơn hàng
                            </Text>
                          </div>
                          <div style={{ marginLeft: 28 }}>
                            <Tag
                              color={statusConfig.color}
                              style={{
                                padding: '6px 12px',
                                borderRadius: 8,
                                fontWeight: 600,
                                fontSize: 14,
                                border: 'none',
                              }}
                            >
                              {statusConfig.label}
                            </Tag>
                          </div>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <CreditCardOutlined style={{ color: '#667eea', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Trạng thái thanh toán
                            </Text>
                          </div>
                          <div style={{ marginLeft: 28 }}>
                            <Tag
                              color={order.paymentStatus ? '#52c41a' : '#faad14'}
                              style={{
                                padding: '6px 12px',
                                borderRadius: 8,
                                fontWeight: 600,
                                fontSize: 14,
                                border: 'none',
                              }}
                            >
                              {order.paymentStatus ? 'Đã thanh toán' : 'Chưa thanh toán'}
                            </Tag>
                          </div>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <CalendarOutlined style={{ color: '#667eea', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Ngày tạo
                            </Text>
                          </div>
                          <Text style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            {new Date(order.createdAt).toLocaleString('vi-VN')}
                          </Text>
                        </div>

                        <div>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 12,
                              marginBottom: 8,
                            }}
                          >
                            <HistoryOutlined style={{ color: '#667eea', fontSize: 16 }} />
                            <Text style={{ color: '#64748b', fontSize: 14, fontWeight: 500 }}>
                              Cập nhật lần cuối
                            </Text>
                          </div>
                          <Text style={{ color: '#1e293b', fontSize: 16, marginLeft: 28 }}>
                            {new Date(order.updatedAt).toLocaleString('vi-VN')}
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>
              ),
            },
            {
              key: '2',
              label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, padding: '8px 16px' }}>
                  <ShoppingCartOutlined style={{ fontSize: 16 }} />
                  <span style={{ fontWeight: 600, fontSize: 16 }}>
                    Thông tin đơn hàng ({order.products?.length || 0})
                  </span>
                </div>
              ),
              children: (
                <div>
                  <Row gutter={[24, 24]}>
                    {/* Products Section */}
                    <Col xs={24} lg={14}>
                      <Card
                        title={
                          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                            <div
                              style={{
                                width: 30,
                                height: 30,
                                borderRadius: 8,
                                background: '#f59e0b',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                              }}
                            >
                              <ShoppingCartOutlined style={{ fontSize: 16 }} />
                            </div>
                            <span style={{ fontWeight: 600, color: '#1e293b', fontSize: 18 }}>
                              Danh sách sản phẩm ({order.products?.length || 0})
                            </span>
                          </div>
                        }
                        className="order-card products-card"
                        style={{
                          borderRadius: 12,
                          border: 'none',
                          overflow: 'hidden',
                          height: '100%',
                        }}
                      >
                        {order.products && order.products.length > 0 ? (
                          <Table
                            columns={productColumns}
                            dataSource={order.products}
                            rowKey="id"
                            pagination={false}
                            scroll={{ x: 600 }}
                            size="small"
                            style={{
                              fontFamily: "'Be Vietnam Pro', sans-serif",
                            }}
                            rowClassName={(_, index) =>
                              index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
                            }
                          />
                        ) : (
                          <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                            <Empty
                              description={
                                <span style={{ color: '#64748b', fontSize: 14 }}>
                                  Không có sản phẩm nào trong đơn hàng này
                                </span>
                              }
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                          </div>
                        )}
                      </Card>
                    </Col>

                    {/* Financial Section */}
                    <Col xs={24} lg={10}>
                      <Card
                        title={
                          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                            <div
                              style={{
                                width: 30,
                                height: 30,
                                borderRadius: 8,
                                background: '#10b981',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: 'white',
                              }}
                            >
                              <DollarOutlined style={{ fontSize: 16 }} />
                            </div>
                            <span style={{ fontWeight: 600, color: '#1e293b', fontSize: 18 }}>
                              Thanh toán
                            </span>
                          </div>
                        }
                        className="order-card financial-card"
                        style={{
                          borderRadius: 12,
                          border: 'none',
                          overflow: 'hidden',
                          height: '100%',
                        }}
                      >
                        <div style={{ padding: '8px 0' }}>
                          {/* Financial Breakdown */}
                          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '8px 0',
                                borderBottom: '1px solid #f1f5f9',
                              }}
                            >
                              <Text style={{ color: '#64748b', fontSize: 14 }}>
                                Tổng tiền hàng:
                              </Text>
                              <Text strong style={{ fontSize: 14, color: '#1e293b' }}>
                                {formatCurrency(
                                  (order.priceAfterTax || 0) -
                                    (order.taxAmount || 0) -
                                    (order.shippingAmount || 0) +
                                    (order.discountAmount || 0)
                                )}
                              </Text>
                            </div>

                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '8px 0',
                                borderBottom: '1px solid #f1f5f9',
                              }}
                            >
                              <Text style={{ color: '#64748b', fontSize: 14 }}>Giảm giá:</Text>
                              <Text style={{ color: '#dc2626', fontSize: 14, fontWeight: 600 }}>
                                -{formatCurrency(order.discountAmount || 0)}
                              </Text>
                            </div>

                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '8px 0',
                                borderBottom: '1px solid #f1f5f9',
                              }}
                            >
                              <Text style={{ color: '#64748b', fontSize: 14 }}>
                                Phí vận chuyển:
                              </Text>
                              <Text style={{ fontSize: 14, color: '#1e293b', fontWeight: 600 }}>
                                {formatCurrency(order.shippingAmount || 0)}
                              </Text>
                            </div>

                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '8px 0',
                                borderBottom: '1px solid #f1f5f9',
                              }}
                            >
                              <Text style={{ color: '#64748b', fontSize: 14 }}>Thuế:</Text>
                              <Text style={{ fontSize: 14, color: '#1e293b', fontWeight: 600 }}>
                                {formatCurrency(order.taxAmount || 0)}
                              </Text>
                            </div>

                            <div
                              style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '12px 0',
                                marginTop: 8,
                                borderTop: '2px solid #e2e8f0',
                              }}
                            >
                              <Text strong style={{ color: '#1e293b', fontSize: 16 }}>
                                Tổng thanh toán:
                              </Text>
                              <Text strong style={{ fontSize: 18, color: '#15803d' }}>
                                {formatCurrency(order.priceAfterTax)}
                              </Text>
                            </div>

                            <div style={{ marginTop: 12, textAlign: 'center' }}>
                              <Text style={{ fontSize: 14, color: '#64748b' }}>
                                {order.paymentStatus ? '✅ Đã thanh toán' : '⏳ Chưa thanh toán'}
                              </Text>
                            </div>
                          </div>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </div>
              ),
            },
            {
              key: '3',
              label: (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, padding: '8px 16px' }}>
                  <PercentageOutlined style={{ fontSize: 16 }} />
                  <span style={{ fontWeight: 600, fontSize: 16 }}>
                    Thông tin hoa hồng ({order.commissions?.length || 0})
                  </span>
                </div>
              ),
              children: (
                <div>
                  <Card
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                        <div
                          style={{
                            width: 30,
                            height: 30,
                            borderRadius: 8,
                            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                          }}
                        >
                          <PercentageOutlined style={{ fontSize: 16 }} />
                        </div>
                        <span style={{ fontWeight: 600, color: '#1e293b', fontSize: 18 }}>
                          Thông tin hoa hồng
                        </span>
                      </div>
                    }
                    className="order-card commission-card"
                    style={{
                      borderRadius: 12,
                      border: 'none',
                      overflow: 'hidden',
                    }}
                  >
                    {order.commissions && order.commissions.length > 0 ? (
                      <Table
                        dataSource={order.commissions}
                        rowKey={(record: any, index?: number) =>
                          record.id || index || Math.random()
                        }
                        pagination={false}
                        size="small"
                        style={{
                          fontFamily: "'Be Vietnam Pro', sans-serif",
                        }}
                        columns={[
                          {
                            title: 'STT',
                            key: 'stt',
                            width: 60,
                            render: (_: any, __: any, index: number) => (
                              <Text style={{ color: '#1e293b', fontSize: 14, fontWeight: 600 }}>
                                {index + 1}
                              </Text>
                            ),
                          },
                          {
                            title: 'Số tiền',
                            dataIndex: 'amount',
                            key: 'amount',
                            width: 150,
                            render: (amount: number) => (
                              <Text strong style={{ color: '#1e293b', fontSize: 14 }}>
                                {formatCurrency(amount)}
                              </Text>
                            ),
                          },
                          {
                            title: 'Tỷ lệ',
                            dataIndex: 'percentage',
                            key: 'percentage',
                            width: 80,
                            render: (percentage: number) => (
                              <Text style={{ color: '#1e293b', fontSize: 14, fontWeight: 600 }}>
                                {percentage}%
                              </Text>
                            ),
                          },
                          {
                            title: 'Người nhận',
                            key: 'recipient',
                            render: (record: any) => {
                              if (record.type === 'referral' && record.user) {
                                return (
                                  <div>
                                    <Text
                                      strong
                                      style={{
                                        color: '#1e293b',
                                        fontSize: 14,
                                        display: 'block',
                                        marginBottom: 2,
                                      }}
                                    >
                                      {record.user.name ||
                                        record.user.username ||
                                        record.user.email}
                                    </Text>
                                    {record.user.phone && (
                                      <div
                                        style={{ display: 'flex', alignItems: 'center', gap: 4 }}
                                      >
                                        <PhoneOutlined style={{ color: '#f59e0b', fontSize: 12 }} />
                                        <Text style={{ color: '#64748b', fontSize: 12 }}>
                                          {record.user.phone}
                                        </Text>
                                      </div>
                                    )}
                                  </div>
                                );
                              } else {
                                return (
                                  <Text
                                    strong
                                    style={{
                                      color: '#1e293b',
                                      fontSize: 14,
                                    }}
                                  >
                                    Công ty
                                  </Text>
                                );
                              }
                            },
                          },
                          {
                            title: 'Trạng thái',
                            dataIndex: 'statusPaid',
                            key: 'statusPaid',
                            width: 120,
                            render: (statusPaid: string) => (
                              <Tag
                                color={
                                  statusPaid === 'paid'
                                    ? '#52c41a'
                                    : statusPaid === 'cancelled'
                                      ? '#ff4d4f'
                                      : '#faad14'
                                }
                                style={{
                                  padding: '4px 8px',
                                  borderRadius: 6,
                                  fontWeight: 500,
                                  fontSize: 12,
                                  border: 'none',
                                }}
                              >
                                {statusPaid === 'paid'
                                  ? 'Đã duyệt'
                                  : statusPaid === 'cancelled'
                                    ? 'Đã hủy'
                                    : 'Chờ duyệt'}
                              </Tag>
                            ),
                          },
                          {
                            title: 'Thao tác',
                            key: 'actions',
                            width: 120,
                            render: (record: any) => (
                              <div style={{ display: 'flex', gap: 8 }}>
                                <Button
                                  type="primary"
                                  size="small"
                                  icon={<EditOutlined />}
                                  onClick={() => handleEditCommission(record)}
                                  style={{
                                    fontSize: 12,
                                    height: 28,
                                    padding: '0 8px',
                                  }}
                                >
                                  Sửa
                                </Button>
                                <Button
                                  type="default"
                                  size="small"
                                  icon={<HistoryOutlined />}
                                  onClick={() => handleViewHistory(record)}
                                  style={{
                                    fontSize: 12,
                                    height: 28,
                                    padding: '0 8px',
                                  }}
                                >
                                  Lịch sử
                                </Button>
                              </div>
                            ),
                          },
                        ]}
                      />
                    ) : (
                      <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                        <Empty
                          description={
                            <span style={{ color: '#64748b', fontSize: 14 }}>
                              Chưa có thông tin hoa hồng cho đơn hàng này
                            </span>
                          }
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                        />
                      </div>
                    )}
                  </Card>
                </div>
              ),
            },
          ]}
        />
      </div>

      {/* Customer Edit Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <div
              style={{
                width: 40,
                height: 40,
                borderRadius: 10,
                background: '#6366f1',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
              }}
            >
              <UserOutlined />
            </div>
            <span style={{ fontWeight: 700, color: '#1e293b', fontSize: 18 }}>
              Chỉnh sửa thông tin khách hàng
            </span>
          </div>
        }
        open={showCustomerModal}
        onCancel={() => setShowCustomerModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowCustomerModal(false)}>
            Hủy
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={updatingCustomer}
            onClick={() => customerForm.submit()}
            style={{
              background: '#6366f1',
              borderColor: '#6366f1',
            }}
          >
            Cập nhật
          </Button>,
        ]}
        width={600}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          <Form
            form={customerForm}
            layout="vertical"
            onFinish={handleCustomerUpdate}
            style={{ fontFamily: "'Be Vietnam Pro', sans-serif" }}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Tên khách hàng"
                  name="name"
                  rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng' }]}
                >
                  <Input
                    placeholder="Nhập tên khách hàng"
                    prefix={<UserOutlined style={{ color: '#6366f1' }} />}
                    style={{ borderRadius: 8 }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Email"
                  name="email"
                  rules={[
                    { type: 'email', message: 'Email không hợp lệ' },
                    { required: true, message: 'Vui lòng nhập email' },
                  ]}
                >
                  <Input
                    placeholder="Nhập email"
                    prefix={<MailOutlined style={{ color: '#6366f1' }} />}
                    style={{ borderRadius: 8 }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name="phone"
                  rules={[{ required: true, message: 'Vui lòng nhập số điện thoại' }]}
                >
                  <Input
                    placeholder="Nhập số điện thoại"
                    prefix={<PhoneOutlined style={{ color: '#6366f1' }} />}
                    style={{ borderRadius: 8 }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label="Địa chỉ"
                  name="address"
                  rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}
                >
                  <Input
                    placeholder="Nhập địa chỉ"
                    prefix={<EnvironmentOutlined style={{ color: '#6366f1' }} />}
                    style={{ borderRadius: 8 }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="Tỉnh/Thành phố"
                  name="provinceCode"
                  rules={[{ required: true, message: 'Vui lòng chọn tỉnh/thành phố' }]}
                >
                  <Select
                    placeholder="Chọn tỉnh/thành phố"
                    style={{ borderRadius: 8 }}
                    onChange={handleProvinceChange}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={provinces.map((province) => ({
                      value: province.code,
                      label: province.name_with_type,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="Quận/Huyện"
                  name="districtCode"
                  rules={[{ required: true, message: 'Vui lòng chọn quận/huyện' }]}
                >
                  <Select
                    placeholder="Chọn quận/huyện"
                    style={{ borderRadius: 8 }}
                    onChange={handleDistrictChange}
                    disabled={!selectedProvinceCode}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={districts.map((district) => ({
                      value: district.code,
                      label: district.name_with_type,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="Phường/Xã"
                  name="wardCode"
                  rules={[{ required: true, message: 'Vui lòng chọn phường/xã' }]}
                >
                  <Select
                    placeholder="Chọn phường/xã"
                    style={{ borderRadius: 8 }}
                    onChange={handleWardChange}
                    disabled={!selectedDistrictCode}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={wards.map((ward) => ({
                      value: ward.code,
                      label: ward.name_with_type,
                    }))}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <div
            style={{
              background: '#f0f9ff',
              padding: '16px',
              borderRadius: 8,
              border: '1px solid #bae6fd',
              marginTop: 16,
            }}
          >
            <Text style={{ color: '#0369a1', fontSize: 14 }}>
              💡 <strong>Lưu ý:</strong> Thông tin khách hàng sẽ được cập nhật cho đơn hàng này.
              Việc thay đổi sẽ được ghi lại trong lịch sử.
            </Text>
            <br />
            <Text style={{ color: '#0369a1', fontSize: 13, marginTop: 8, display: 'block' }}>
              ⚠️ <strong>Hạn chế:</strong> Chỉ có thể sửa thông tin khách hàng khi đơn hàng ở trạng
              thái "Chờ xác nhận" hoặc "Chờ giao hàng".
            </Text>
          </div>
        </div>
      </Modal>

      {/* Status Update Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <div
              style={{
                width: 40,
                height: 40,
                borderRadius: 10,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
              }}
            >
              <EditOutlined />
            </div>
            <span style={{ fontWeight: 700, color: '#1e293b', fontSize: 18 }}>
              Cập nhật trạng thái đơn hàng
            </span>
          </div>
        }
        open={showStatusModal}
        onCancel={() => setShowStatusModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowStatusModal(false)}>
            Hủy
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={updatingStatus}
            onClick={() => handleStatusChange(selectedStatus)}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderColor: '#667eea',
            }}
          >
            Cập nhật
          </Button>,
        ]}
        width={500}
        centered
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: 16 }}>
            <Text style={{ color: '#64748b', fontSize: 14 }}>
              Trạng thái hiện tại:{' '}
              <Text strong style={{ color: statusConfig.color }}>
                {statusConfig.label}
              </Text>
            </Text>
          </div>

          <div style={{ marginBottom: 20 }}>
            <Text
              style={{
                color: '#1e293b',
                fontSize: 16,
                fontWeight: 600,
                marginBottom: 8,
                display: 'block',
              }}
            >
              Chọn trạng thái mới:
            </Text>
            <div
              style={{
                marginBottom: 12,
                padding: '8px 12px',
                background: '#f0f9ff',
                borderRadius: 6,
                border: '1px solid #bae6fd',
              }}
            >
              <Text style={{ color: '#0369a1', fontSize: 13 }}>
                💡 Chỉ có thể chuyển tiến trạng thái hoặc hủy đơn hàng. Không thể chuyển ngược trạng
                thái.
              </Text>
            </div>
            <Select
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
              size="large"
              placeholder="Chọn trạng thái"
            >
              {getAvailableStatusOptions(order.statusOrder).map((option) => (
                <Select.Option key={option.value} value={option.value}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    {option.icon}
                    <span style={{ color: option.color, fontWeight: 500 }}>{option.label}</span>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </div>

          <div
            style={{
              background: '#f8fafc',
              padding: '16px',
              borderRadius: 8,
              border: '1px solid #e2e8f0',
            }}
          >
            <Text style={{ color: '#64748b', fontSize: 14 }}>
              💡 <strong>Lưu ý:</strong> Việc thay đổi trạng thái đơn hàng sẽ được ghi lại trong
              lịch sử và không thể hoàn tác.
            </Text>
          </div>
        </div>
      </Modal>

      {/* Commission Edit Modal */}
      <Modal
        title="Chỉnh sửa thông tin hoa hồng"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleCommissionUpdate}
          style={{ marginTop: 16 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Tỷ lệ hoa hồng (%)"
                name="percentage"
                rules={[{ required: true, message: 'Vui lòng nhập tỷ lệ' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  step={0.1}
                  placeholder="Nhập tỷ lệ %"
                  onChange={handlePercentageChange}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Số tiền hoa hồng (tự động tính)">
                <div
                  style={{
                    padding: '8px 12px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#f5f5f5',
                    color: '#666',
                    fontSize: '14px',
                    fontWeight: 600,
                  }}
                >
                  {formatCurrency(calculatedAmount)}
                </div>
                <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                  Tính từ: {formatCurrency(order.priceAfterTax || 0)} ×{' '}
                  {editForm.getFieldValue('percentage') || 0}%
                </div>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="Lý do thay đổi"
            name="reason"
            rules={[{ required: true, message: 'Vui lòng nhập lý do thay đổi' }]}
          >
            <Input.TextArea rows={3} placeholder="Nhập lý do thay đổi thông tin hoa hồng..." />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>Hủy</Button>
              <Button type="primary" htmlType="submit">
                Cập nhật
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Commission History Modal */}
      <Modal
        title="Lịch sử thay đổi hoa hồng"
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHistoryModalVisible(false)}>
            Đóng
          </Button>,
        ]}
        width={800}
      >
        {auditLogs.length === 0 ? (
          <Empty description="Chưa có lịch sử thay đổi nào" style={{ margin: '40px 0' }} />
        ) : (
          <Table
            dataSource={auditLogs}
            rowKey="id"
            pagination={false}
            size="small"
            columns={[
              {
                title: 'Thời gian',
                dataIndex: 'createdAt',
                key: 'createdAt',
                width: 150,
                render: (date: string) => new Date(date).toLocaleString('vi-VN'),
              },
              {
                title: 'Trường',
                dataIndex: 'field',
                key: 'field',
                width: 100,
                render: (field: string) => {
                  const fieldNames: { [key: string]: string } = {
                    amount: 'Số tiền',
                    percentage: 'Tỷ lệ',
                    statusPaid: 'Trạng thái',
                  };
                  return fieldNames[field] || field;
                },
              },
              {
                title: 'Giá trị cũ',
                dataIndex: 'oldValue',
                key: 'oldValue',
                width: 120,
                render: (value: string, record: any) => {
                  if (record.field === 'amount') {
                    return formatCurrency(parseInt(value));
                  }
                  if (record.field === 'statusPaid') {
                    const statusNames: { [key: string]: string } = {
                      pending: 'Chờ duyệt',
                      paid: 'Đã duyệt',
                      cancelled: 'Đã hủy',
                    };
                    return statusNames[value] || value;
                  }
                  if (record.field === 'percentage') {
                    return `${value}%`;
                  }
                  return value;
                },
              },
              {
                title: 'Giá trị mới',
                dataIndex: 'newValue',
                key: 'newValue',
                width: 120,
                render: (value: string, record: any) => {
                  if (record.field === 'amount') {
                    return formatCurrency(parseInt(value));
                  }
                  if (record.field === 'statusPaid') {
                    const statusNames: { [key: string]: string } = {
                      pending: 'Chờ duyệt',
                      paid: 'Đã duyệt',
                      cancelled: 'Đã hủy',
                    };
                    return statusNames[value] || value;
                  }
                  if (record.field === 'percentage') {
                    return `${value}%`;
                  }
                  return value;
                },
              },
              {
                title: 'Người thay đổi',
                dataIndex: 'changedBy',
                key: 'changedBy',
                render: (user: any) => {
                  if (!user) return 'N/A';
                  return user.firstname && user.lastname
                    ? `${user.firstname} ${user.lastname}`
                    : user.username || user.email;
                },
              },
              {
                title: 'Lý do',
                dataIndex: 'reason',
                key: 'reason',
                ellipsis: true,
              },
            ]}
          />
        )}
      </Modal>
    </div>
  );
};

export default OrderDetail;
