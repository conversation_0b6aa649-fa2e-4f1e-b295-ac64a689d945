import React from 'react';
import { Image } from 'antd';
import { ImageContainer, PlaceholderContainer } from './StyledComponents';

interface ImageDisplayProps {
  src?: string;
  alt?: string;
  size?: number;
  placeholder?: string;
  preview?: boolean;
  previewSrc?: string;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({
  src,
  alt = 'Image',
  size = 40,
  placeholder = 'Không có',
  preview = true,
  previewSrc,
}) => {
  if (!src) {
    return (
      <PlaceholderContainer size={size}>
        {placeholder}
      </PlaceholderContainer>
    );
  }

  return (
    <ImageContainer size={size}>
      <Image
        src={src}
        alt={alt}
        preview={preview ? {
          src: previewSrc || src,
        } : false}
      />
    </ImageContainer>
  );
};

export default ImageDisplay;
