export default (_config: any) => {
  return async (ctx: any, next: any) => {
    // Check if this is a GET request to root paths that should redirect
    if (
      ctx.method === "GET" &&
      (ctx.path === "/" || ctx.path === "/index.html" || ctx.path === "/admin")
    ) {
      // Skip redirect if this is an API request or already in admin area
      if (
        ctx.path.startsWith("/api/") ||
        ctx.path.startsWith("/admin/plugins/") ||
        ctx.path.startsWith("/admin/content-manager/") ||
        ctx.path.startsWith("/admin/settings/") ||
        ctx.path.startsWith("/uploads/")
      ) {
        return next();
      }

      // Redirect to management plugin
      return ctx.redirect("/admin/plugins/management");
    }

    // Continue to next middleware
    return next();
  };
};
