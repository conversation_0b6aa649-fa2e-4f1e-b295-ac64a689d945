import React from 'react';
import { Card<PERSON><PERSON>er, HeaderInfo, CardTitle, CardDescription, HeaderActions } from './StyledComponents';

interface PageHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, description, actions }) => {
  return (
    <CardHeader>
      <HeaderInfo>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </HeaderInfo>
      {actions && <HeaderActions>{actions}</HeaderActions>}
    </CardHeader>
  );
};

export default PageHeader;
