import { useState, useEffect } from 'react';

// Import JS data
import provincesData from '../data/province';
import districtsData from '../data/district';
import wardsData from '../data/ward';

export interface Province {
  name: string;
  slug: string;
  type: string;
  name_with_type: string;
  code: string;
}

export interface District {
  name: string;
  type: string;
  slug: string;
  name_with_type: string;
  path: string;
  path_with_type: string;
  code: string;
  parent_code: string;
}

export interface Ward {
  name: string;
  type: string;
  slug: string;
  name_with_type: string;
  path: string;
  path_with_type: string;
  code: string;
  parent_code: string;
}

export const useAddressData = () => {
  const [provinces] = useState<Province[]>(provincesData);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);

  const getDistrictsByProvince = (provinceCode: string): District[] => {
    return districtsData.filter((district: District) => district.parent_code === provinceCode);
  };

  const getWardsByDistrict = (districtCode: string): Ward[] => {
    return wardsData.filter((ward: Ward) => ward.parent_code === districtCode);
  };

  const updateDistricts = (provinceCode: string) => {
    const filteredDistricts = getDistrictsByProvince(provinceCode);
    setDistricts(filteredDistricts);
    setWards([]); // Reset wards when province changes
  };

  const updateWards = (districtCode: string) => {
    const filteredWards = getWardsByDistrict(districtCode);
    setWards(filteredWards);
  };

  // Helper functions to get names by code
  const getProvinceName = (provinceCode: string): string => {
    const province = provinces.find((p) => p.code === provinceCode);
    return province ? province.name_with_type : '';
  };

  const getDistrictName = (districtCode: string): string => {
    const district = districtsData.find((d: District) => d.code === districtCode);
    return district ? district.name_with_type : '';
  };

  const getWardName = (wardCode: string): string => {
    const ward = wardsData.find((w: Ward) => w.code === wardCode);
    return ward ? ward.name_with_type : '';
  };

  // Helper function to find codes by names (for reverse lookup)
  const getProvinceCodeByName = (provinceName: string): string => {
    const province = provinces.find(
      (p) =>
        p.name === provinceName ||
        p.name_with_type === provinceName ||
        p.name_with_type.includes(provinceName)
    );
    return province ? province.code : '';
  };

  const getDistrictCodeByName = (districtName: string, provinceCode?: string): string => {
    let searchDistricts = districtsData;
    if (provinceCode) {
      searchDistricts = districtsData.filter((d: District) => d.parent_code === provinceCode);
    }
    const district = searchDistricts.find(
      (d: District) =>
        d.name === districtName ||
        d.name_with_type === districtName ||
        d.name_with_type.includes(districtName)
    );
    return district ? district.code : '';
  };

  const getWardCodeByName = (wardName: string, districtCode?: string): string => {
    let searchWards = wardsData;
    if (districtCode) {
      searchWards = wardsData.filter((w: Ward) => w.parent_code === districtCode);
    }
    const ward = searchWards.find(
      (w: Ward) =>
        w.name === wardName || w.name_with_type === wardName || w.name_with_type.includes(wardName)
    );
    return ward ? ward.code : '';
  };

  return {
    provinces,
    districts,
    wards,
    updateDistricts,
    updateWards,
    getProvinceName,
    getDistrictName,
    getWardName,
    getProvinceCodeByName,
    getDistrictCodeByName,
    getWardCodeByName,
    getDistrictsByProvince,
    getWardsByDistrict,
  };
};
