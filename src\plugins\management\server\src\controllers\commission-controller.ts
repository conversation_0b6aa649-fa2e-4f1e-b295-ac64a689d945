import type { Core } from '@strapi/strapi';

const commissionController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listCommissions(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('commission-service')
      .listCommissions(ctx.query);
  },

  async getCommissionStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('commission-service')
      .getCommissionStatistics(ctx.query);
  },

  async updateCommission(ctx) {
    try {
      const { id } = ctx.params;
      const { percentage, reason } = ctx.request.body;
      const adminUser = ctx.state.user;

      // Validate ID parameter
      const numericId = parseInt(id, 10);
      if (isNaN(numericId) || numericId <= 0) {
        ctx.throw(400, 'Invalid commission ID');
      }

      const result = await strapi
        .plugin('management')
        .service('commission-service')
        .updateCommission(numericId, { percentage, reason }, adminUser);

      ctx.body = {
        success: true,
        data: result,
        message: 'Cập nhật hoa hồng thành công',
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },

  async getCommissionAuditLogs(ctx) {
    try {
      const { id } = ctx.params;

      // Validate ID parameter
      const numericId = parseInt(id, 10);
      if (isNaN(numericId) || numericId <= 0) {
        ctx.throw(400, 'Invalid commission ID');
      }

      const result = await strapi
        .plugin('management')
        .service('commission-service')
        .getCommissionAuditLogs(numericId);

      ctx.body = {
        success: true,
        data: result.data,
      };
    } catch (error) {
      ctx.throw(500, error.message);
    }
  },
});

export default commissionController;
