# name: Deploy oferify-strapi TTKOREA TEST

# on:
#   push:
#     branches:
#       - 'ttkorea'

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     env:
#       NODE_OPTIONS: '--max_old_space_size=4096'
#       URL: https://test-admin.ttkoreagroupvn.com
#     strategy:
#       matrix:
#         node-version: [14.x]
#     steps:
#       - uses: actions/checkout@v1
#       - run: yarn
#       - run: yarn build
#       - run: zip -r build.zip build

#       - name: copy file via ssh password
#         uses: appleboy/scp-action@master
#         with:
#           host: ${{ secrets.HOST_TTKOREA }}
#           username: ${{ secrets.USERNAME }}
#           password: ${{ secrets.PASSWORD_TTKOREA }}
#           port: ${{ secrets.PORT_TTKOREA }}
#           source: "build.zip"
#           target: "/root/test-ttkorea/oferify-strapi"

#       - name: executing remote ssh commands using password
#         uses: appleboy/ssh-action@master
#         with:
#           host: ${{ secrets.HOST_TTKOREA }}
#           username: ${{ secrets.USERNAME }}
#           password: ${{ secrets.PASSWORD_TTKOREA }}
#           port: ${{ secrets.PORT_TTKOREA }}
#           script: |
#             cd /root/test-ttkorea/oferify-strapi
#             git pull
#             git status
#             yarn
#             unzip build.zip -d build2
#             mv build build3
#             mv build2/build build
#             rm -rf build2 build3
#             pm2 restart test-oferify-strapi

#       - name: Send mail
#         uses: dawidd6/action-send-mail@v3
#         with:
#           connection_url: ${{secrets.MAIL_CONNECTION}}
#           server_address: smtp.gmail.com
#           server_port: 465
#           secure: true
#           subject: 'Deploy test: ${{github.repository}}: ${{ github.event.head_commit.message }}'
#           to: <EMAIL>
#           from: GithubActionBot
#           body: Build job of ${{github.repository}} completed successfully!
#           ignore_cert: true
#           convert_markdown: true
#           priority: low
