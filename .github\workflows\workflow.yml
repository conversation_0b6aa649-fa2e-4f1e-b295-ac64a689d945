name: Deploy STRAPI PROD

on:
  push:
    branches:
      - "main"

env:
  REPO: base

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: "--max_old_space_size=6144"
      URL: https://base.libertyholding.com.vn
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js 20.19.3
        uses: actions/setup-node@v3
        with:
          node-version: 20.19.3
      - run: |
          yarn install-lib
          yarn build
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          zip -r build.zip dist/build

      - name: copy file via ssh password
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          source: "build.zip"
          target: "/root/${{ env.REPO }}/${{ env.REPO }}-strapi"

      - name: executing remote ssh commands using password
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          script: |
            cd /root/${{ env.REPO }}/${{ env.REPO }}-strapi
            export PATH="$PATH:/root/.nvm/versions/node/v18.20.4/bin"
            git pull
            git status
            yarn
            unzip build.zip -d build2
            mv build build3
            mv build2/build build
            rm -rf build2 build3
            pm2 restart ${{ env.REPO }}-strapi
