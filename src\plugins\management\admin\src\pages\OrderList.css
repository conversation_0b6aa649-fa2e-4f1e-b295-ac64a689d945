/* OrderList Custom Styles */
.order-list-container {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubunt<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.table-row-light {
  background-color: #fafafa;
}

.table-row-dark {
  background-color: #ffffff;
}

.table-row-light:hover,
.table-row-dark:hover {
  background-color: #e6f7ff !important;
  transition: background-color 0.3s ease;
}

.order-list-container .ant-table-thead > tr > th {
  background: #1e293b !important;
  color: white !important;
  font-weight: 600 !important;
  border: none !important;
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-list-container .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 16px !important;
}

.order-list-container .ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e2e8f0 !important;
}

.order-list-container .ant-btn {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-list-container .ant-input {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-list-container .ant-select {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-list-container .ant-pagination {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

.order-list-container .ant-spin-text {
  font-family: 'Be Vietnam Pro', sans-serif !important;
}

/* Gradient text effect */
.gradient-text {
  color: #1e293b !important;
  font-weight: 700 !important;
}

/* Card hover effects */
.order-card {
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Button animations */
.order-list-container .ant-btn {
  transition: all 0.3s ease;
}

.order-list-container .ant-btn:hover {
  transform: translateY(-1px);
}

/* Status tag animations */
.order-list-container .ant-tag {
  transition: all 0.3s ease;
}

.order-list-container .ant-tag:hover {
  transform: scale(1.05);
}

/* Custom Tabs Styling */
.order-list-container .ant-tabs-tab {
  font-family: 'Be Vietnam Pro', sans-serif !important;
  font-weight: 600 !important;
  padding: 12px 20px !important;
  margin-right: 8px !important;
  border-radius: 8px 8px 0 0 !important;
  transition: all 0.3s ease !important;
  background: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  border-bottom: none !important;
}

.order-list-container .ant-tabs-tab:hover {
  background: #e2e8f0 !important;
  transform: translateY(-2px) !important;
}

.order-list-container .ant-tabs-tab-active {
  background: #ffffff !important;
  border-color: #1e293b !important;
  color: #1e293b !important;
  box-shadow: 0 -2px 8px rgba(30, 41, 59, 0.1) !important;
}

.order-list-container .ant-tabs-tab-active:hover {
  background: #ffffff !important;
  transform: translateY(-2px) !important;
}

.order-list-container .ant-tabs-content-holder {
  background: #ffffff !important;
  border-radius: 0 0 12px 12px !important;
}

.order-list-container .ant-tabs-ink-bar {
  background: #1e293b !important;
  height: 3px !important;
  border-radius: 2px !important;
}

/* Badge styling in tabs */
.order-list-container .ant-tabs-tab .ant-badge {
  margin-left: 8px !important;
}

.order-list-container .ant-tabs-tab .ant-badge-count {
  font-size: 11px !important;
  font-weight: 600 !important;
  min-width: 18px !important;
  height: 18px !important;
  line-height: 18px !important;
  border-radius: 9px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}
