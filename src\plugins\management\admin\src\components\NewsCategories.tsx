import React, { useState, useEffect } from 'react';
import { Table, Button, message, Switch } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import {
  PageContainer,
  Card,
  CardContent,
  FiltersSection,
  SearchBar,
  PageHeader,
  ActionButtonGroup,
  ImageDisplay,
  StyledTable,
  CategoryBrandModal,
} from './shared';

interface NewsCategory {
  id: number;
  documentId: string;
  name: string;
  description?: string;
  image?: {
    id: number;
    url: string;
    name: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const NewsCategories: React.FC = () => {
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<NewsCategory | null>(null);
  const [deleting, setDeleting] = useState<number | null>(null);

  const { get, post, put, del } = useFetchClient();

  // Fetch categories
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await get('/management/news/categories');
      setCategories(response.data.data || []);
    } catch (error: any) {
      console.error('Error fetching news categories:', error);
      message.error('Có lỗi xảy ra khi tải danh mục tin tức');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle modal success
  const handleModalSuccess = () => {
    setModalVisible(false);
    setEditingCategory(null);
    fetchCategories();
  };

  // Handle delete category
  const handleDelete = async (id: number) => {
    if (deleting === id) return; // Prevent double deletion

    setDeleting(id);
    try {
      await del(`/management/news/categories/${id}`);
      message.success('Xóa danh mục tin tức thành công');
      fetchCategories();
    } catch (error: any) {
      console.error('Error deleting news category:', error);
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa danh mục tin tức');
    } finally {
      setDeleting(null);
    }
  };

  // Handle edit
  const handleEdit = (category: NewsCategory) => {
    setEditingCategory(category);
    setModalVisible(true);
  };

  // Handle add new
  const handleAdd = () => {
    setEditingCategory(null);
    setModalVisible(true);
  };

  // Filter categories based on search
  const filteredCategories = categories.filter((category) =>
    category.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Hình ảnh',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      render: (image: any) => (
        <ImageDisplay
          src={image?.url}
          alt="News Category"
          size={50}
          placeholder="Không có"
          preview={true}
        />
      ),
    },
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: NewsCategory, b: NewsCategory) => a.name.localeCompare(b.name),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => description || 'Không có mô tả',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 120,
      render: (isActive: boolean) => (
        <Switch
          checked={isActive}
          disabled
          size="small"
          checkedChildren="Hoạt động"
          unCheckedChildren="Tạm dừng"
        />
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
      sorter: (a: NewsCategory, b: NewsCategory) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_: any, record: NewsCategory) => (
        <ActionButtonGroup
          onEdit={() => handleEdit(record)}
          onDelete={() => handleDelete(record.id)}
          deleteConfirmTitle="Xác nhận xóa"
          deleteConfirmDescription="Bạn có chắc chắn muốn xóa danh mục tin tức này?"
          showView={false}
          showEdit={true}
          showDelete={true}
          editTooltip="Chỉnh sửa danh mục tin tức"
          deleteTooltip="Xóa danh mục tin tức"
          deleteLoading={deleting === record.id}
          disabled={deleting !== null}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Card>
        <PageHeader
          title="Quản lý danh mục tin tức"
          description="Quản lý các danh mục tin tức trong hệ thống"
          actions={
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} size="large">
              Thêm danh mục tin tức
            </Button>
          }
        />

        <CardContent>
          <FiltersSection>
            <SearchBar
              placeholder="Tìm kiếm danh mục tin tức..."
              value={searchText}
              onChange={setSearchText}
            />
          </FiltersSection>

          <StyledTable>
            <Table
              columns={columns}
              dataSource={filteredCategories}
              rowKey="id"
              loading={loading}
              pagination={{
                total: filteredCategories.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} của ${total} danh mục tin tức`,
              }}
              scroll={{ x: 800 }}
            />
          </StyledTable>
        </CardContent>
      </Card>

      <CategoryBrandModal
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingCategory(null);
        }}
        type="news-category"
        editingItem={editingCategory}
        onSuccess={handleModalSuccess}
        showActiveSwitch={true}
      />
    </PageContainer>
  );
};

export default NewsCategories;
