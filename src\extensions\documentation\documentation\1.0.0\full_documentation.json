{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "DOCUMENTATION", "description": "", "termsOfService": "YOUR_TERMS_OF_SERVICE_URL", "contact": {"name": "TEAM", "email": "<EMAIL>", "url": "mywebsite.io"}, "license": {"name": "Apache 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}, "x-generation-date": "2025-07-09T16:22:58.090Z"}, "x-strapi-config": {"plugins": ["upload", "users-permissions"]}, "servers": [{"url": "http://localhost:1337/api", "description": "Development server"}], "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"}, "security": [{"bearerAuth": []}], "paths": {"/bai-viets": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Bai-viet"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/bai-viets"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Bai-viet"], "parameters": [], "operationId": "post/bai-viets", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietRequest"}}}}}}, "/bai-viets/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Bai-viet"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/bai-viets/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Bai-viet"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/bai-viets/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaiVietRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Bai-viet"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/bai-viets/{id}"}}, "/danh-muc-san-phams": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON><PERSON>-muc-san-pham"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/danh-muc-san-phams"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON><PERSON>-muc-san-pham"], "parameters": [], "operationId": "post/danh-muc-san-phams", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamRequest"}}}}}}, "/danh-muc-san-phams/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON><PERSON>-muc-san-pham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/danh-muc-san-phams/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON><PERSON>-muc-san-pham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/danh-muc-san-phams/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DanhMucSanPhamRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON><PERSON>-muc-san-pham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/danh-muc-san-phams/{id}"}}, "/don-hangs": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Don-hang"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/don-hangs"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Don-hang"], "parameters": [], "operationId": "post/don-hangs", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangRequest"}}}}}}, "/don-hangs/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Don-hang"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/don-hangs/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Don-hang"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/don-hangs/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonHangRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Don-hang"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/don-hangs/{id}"}}, "/san-phams": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamListResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON>-<PERSON>ham"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/san-phams"}, "post": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON>-<PERSON>ham"], "parameters": [], "operationId": "post/san-phams", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamRequest"}}}}}}, "/san-phams/{id}": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON>-<PERSON>ham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "get/san-phams/{id}"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON>-<PERSON>ham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "put/san-phams/{id}", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SanPhamRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["<PERSON>-<PERSON>ham"], "parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "number"}}], "operationId": "delete/san-phams/{id}"}}, "/thiet-lap-giao-dien": {"get": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThietLapGiaoDienResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Thiet-lap-giao-dien"], "parameters": [{"name": "sort", "in": "query", "description": "Sort by attributes ascending (asc) or descending (desc)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "pagination[withCount]", "in": "query", "description": "Return page/pageSize (default: true)", "deprecated": false, "required": false, "schema": {"type": "boolean"}}, {"name": "pagination[page]", "in": "query", "description": "Page number (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[pageSize]", "in": "query", "description": "Page size (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[start]", "in": "query", "description": "Offset value (default: 0)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "pagination[limit]", "in": "query", "description": "Number of entities to return (default: 25)", "deprecated": false, "required": false, "schema": {"type": "integer"}}, {"name": "fields", "in": "query", "description": "Fields to return (ex: title,author)", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "populate", "in": "query", "description": "Relations to return", "deprecated": false, "required": false, "schema": {"type": "string"}}, {"name": "filters", "in": "query", "description": "Filters to apply", "deprecated": false, "required": false, "schema": {"type": "object", "additionalProperties": true}, "style": "deepObject"}, {"name": "locale", "in": "query", "description": "Locale to apply", "deprecated": false, "required": false, "schema": {"type": "string"}}], "operationId": "get/thiet-lap-giao-dien"}, "put": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThietLapGiaoDienResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Thiet-lap-giao-dien"], "parameters": [], "operationId": "put/thiet-lap-giao-dien", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ThietLapGiaoDienRequest"}}}}}, "delete": {"responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "integer", "format": "int64"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "tags": ["Thiet-lap-giao-dien"], "parameters": [], "operationId": "delete/thiet-lap-giao-dien"}}, "/upload": {"post": {"description": "Upload files", "responses": {"200": {"description": "response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "summary": "", "tags": ["Upload - File"], "requestBody": {"description": "Upload files", "required": true, "content": {"multipart/form-data": {"schema": {"required": ["files"], "type": "object", "properties": {"path": {"type": "string", "description": "The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."}, "refId": {"type": "string", "description": "The ID of the entry which the file(s) will be linked to"}, "ref": {"type": "string", "description": "The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."}, "field": {"type": "string", "description": "The field of the entry which the file(s) will be precisely linked to."}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}}}, "/upload?id={id}": {"post": {"parameters": [{"name": "id", "in": "query", "description": "File id", "required": true, "schema": {"type": "string"}}], "description": "Upload file information", "responses": {"200": {"description": "response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "summary": "", "tags": ["Upload - File"], "requestBody": {"description": "Upload files", "required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"fileInfo": {"type": "object", "properties": {"name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}}}, "files": {"type": "string", "format": "binary"}}}}}}}}, "/upload/files": {"get": {"tags": ["Upload - File"], "responses": {"200": {"description": "Get a list of files", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UploadFile"}}}}}}}}, "/upload/files/{id}": {"get": {"parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "string"}}], "tags": ["Upload - File"], "responses": {"200": {"description": "Get a specific file", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadFile"}}}}}}, "delete": {"parameters": [{"name": "id", "in": "path", "description": "", "deprecated": false, "required": true, "schema": {"type": "string"}}], "tags": ["Upload - File"], "responses": {"200": {"description": "Delete a file", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadFile"}}}}}}}, "/connect/{provider}": {"get": {"parameters": [{"name": "provider", "in": "path", "required": true, "description": "Provider name", "schema": {"type": "string", "pattern": ".*"}}], "tags": ["Users-Permissions - Auth"], "summary": "Login with a provider", "description": "Redirects to provider login before being redirect to /auth/{provider}/callback", "responses": {"301": {"description": "Redirect response"}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/local": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Local login", "description": "Returns a jwt token and user info", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"identifier": {"type": "string"}, "password": {"type": "string"}}}, "example": {"identifier": "foobar", "password": "Test1234"}}}, "required": true}, "responses": {"200": {"description": "Connection", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/local/register": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Register a user", "description": "Returns a jwt token and user info", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foobar", "email": "<EMAIL>", "password": "Test1234"}}}, "required": true}, "responses": {"200": {"description": "Successful registration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/{provider}/callback": {"get": {"tags": ["Users-Permissions - Auth"], "summary": "Default Callback from provider auth", "parameters": [{"name": "provider", "in": "path", "required": true, "description": "Provider name", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/forgot-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Send rest password email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}, "example": {"email": "<EMAIL>"}}}}, "responses": {"200": {"description": "Returns ok", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/reset-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Rest user password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"password": {"type": "string"}, "passwordConfirmation": {"type": "string"}, "code": {"type": "string"}}}, "example": {"password": "Test1234", "passwordConfirmation": "Test1234", "code": "zertyoaizndoianzodianzdonaizdoinaozdnia"}}}}, "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/change-password": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Update user's own password", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["password", "currentPassword", "passwordConfirmation"], "properties": {"password": {"type": "string"}, "currentPassword": {"type": "string"}, "passwordConfirmation": {"type": "string"}}}}}}, "responses": {"200": {"description": "Returns a jwt token and user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-UserRegistration"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/email-confirmation": {"get": {"tags": ["Users-Permissions - Auth"], "summary": "Confirm user email", "parameters": [{"in": "query", "name": "confirmation", "schema": {"type": "string"}, "description": "confirmation token received by email"}], "responses": {"301": {"description": "Redirects to the configure email confirmation redirect url"}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/send-email-confirmation": {"post": {"tags": ["Users-Permissions - Auth"], "summary": "Send confirmation email", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}}}}}}, "responses": {"200": {"description": "Returns email and boolean to confirm email was sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string"}, "sent": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/permissions": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get default generated permissions", "responses": {"200": {"description": "Returns the permissions tree", "content": {"application/json": {"schema": {"type": "object", "properties": {"permissions": {"$ref": "#/components/schemas/Users-Permissions-PermissionsTree"}}}, "example": {"permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": false, "policy": ""}, "findOne": {"enabled": false, "policy": ""}, "create": {"enabled": false, "policy": ""}}, "controllerB": {"find": {"enabled": false, "policy": ""}, "findOne": {"enabled": false, "policy": ""}, "create": {"enabled": false, "policy": ""}}}}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "List roles", "responses": {"200": {"description": "Returns list of roles", "content": {"application/json": {"schema": {"type": "object", "properties": {"roles": {"type": "array", "items": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-Role"}, {"type": "object", "properties": {"nb_users": {"type": "number"}}}]}}}}, "example": {"roles": [{"id": 1, "name": "Public", "description": "Default role given to unauthenticated user.", "type": "public", "createdAt": "2022-05-19T17:35:35.097Z", "updatedAt": "2022-05-31T16:05:36.603Z", "nb_users": 0}]}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Create a role", "requestBody": {"$ref": "#/components/requestBodies/Users-Permissions-RoleRequest"}, "responses": {"200": {"description": "Returns ok if the role was create", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles/{id}": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get a role", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "responses": {"200": {"description": "Returns the role", "content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}, "example": {"role": {"id": 1, "name": "Public", "description": "Default role given to unauthenticated user.", "type": "public", "createdAt": "2022-05-19T17:35:35.097Z", "updatedAt": "2022-05-31T16:05:36.603Z", "permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": true}}}}}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users-permissions/roles/{role}": {"put": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Update a role", "parameters": [{"in": "path", "name": "role", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "requestBody": {"$ref": "#/components/requestBodies/Users-Permissions-RoleRequest"}, "responses": {"200": {"description": "Returns ok if the role was udpated", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Delete a role", "parameters": [{"in": "path", "name": "role", "required": true, "schema": {"type": "string"}, "description": "role Id"}], "responses": {"200": {"description": "Returns ok if the role was delete", "content": {"application/json": {"schema": {"type": "object", "properties": {"ok": {"type": "string", "enum": [true]}}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get list of users", "responses": {"200": {"description": "Returns an array of users", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Users-Permissions-User"}}, "example": [{"id": 9, "username": "<EMAIL>", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-06-01T18:32:35.211Z", "updatedAt": "2022-06-01T18:32:35.217Z"}]}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Create a user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"email": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foo", "email": "<EMAIL>", "password": "foo-password"}}}}, "responses": {"201": {"description": "Returns created user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}, {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z", "role": {"id": 1, "name": "X", "description": "Default role given to authenticated user.", "type": "authenticated", "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-06-04T07:11:59.551Z"}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/{id}": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "responses": {"200": {"description": "Returns a user", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-User"}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Update a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "email", "password"], "properties": {"email": {"type": "string"}, "username": {"type": "string"}, "password": {"type": "string"}}}, "example": {"username": "foo", "email": "<EMAIL>", "password": "foo-password"}}}}, "responses": {"200": {"description": "Returns updated user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}, {"type": "object", "properties": {"role": {"$ref": "#/components/schemas/Users-Permissions-Role"}}}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z", "role": {"id": 1, "name": "X", "description": "Default role given to authenticated user.", "type": "authenticated", "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-06-04T07:11:59.551Z"}}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Delete a user", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "user Id"}], "responses": {"200": {"description": "Returns deleted user info", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Users-Permissions-User"}]}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/me": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get authenticated user info", "responses": {"200": {"description": "Returns user info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Users-Permissions-User"}, "example": {"id": 1, "username": "foo", "email": "<EMAIL>", "provider": "local", "confirmed": false, "blocked": false, "createdAt": "2022-05-19T17:35:35.096Z", "updatedAt": "2022-05-19T17:35:35.096Z"}}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/count": {"get": {"tags": ["Users-Permissions - Users & Roles"], "summary": "Get user count", "responses": {"200": {"description": "Returns a number", "content": {"application/json": {"schema": {"type": "number"}, "example": 1}}}, "default": {"description": "Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Error": {"type": "object", "required": ["error"], "properties": {"data": {"nullable": true, "oneOf": [{"type": "object"}, {"type": "array", "items": {"type": "object"}}]}, "error": {"type": "object", "properties": {"status": {"type": "integer"}, "name": {"type": "string"}, "message": {"type": "string"}, "details": {"type": "object"}}}}}, "BaiVietRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["title", "hot", "content", "image"], "type": "object", "properties": {"title": {"type": "string"}, "hot": {"type": "boolean"}, "content": {"type": "string"}, "image": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "BaiVietListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BaiViet"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "BaiViet": {"type": "object", "required": ["title", "hot", "content", "image"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "title": {"type": "string"}, "hot": {"type": "boolean"}, "content": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "pathId": {"type": "integer"}, "parent": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "path": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "title": {"type": "string"}, "hot": {"type": "boolean"}, "content": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "BaiVietResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/BaiViet"}, "meta": {"type": "object"}}}, "DanhMucSanPhamRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["name", "image"], "type": "object", "properties": {"name": {"type": "string"}, "image": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "DanhMucSanPhamListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DanhMucSanPham"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "DanhMucSanPham": {"type": "object", "required": ["name", "image"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "pathId": {"type": "integer"}, "parent": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "path": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "DanhMucSanPhamResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/DanhMucSanPham"}, "meta": {"type": "object"}}}, "DonHangRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["code", "priceAfterTax", "taxAmount", "discountAmount", "shippingAmount", "statusOrder", "paymentStatus"], "type": "object", "properties": {"code": {"type": "string"}, "priceAfterTax": {"type": "integer"}, "taxAmount": {"type": "integer"}, "discountAmount": {"type": "integer"}, "shippingAmount": {"type": "integer"}, "orderItems": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "statusOrder": {"type": "string", "enum": ["<PERSON>ờ x<PERSON>c n<PERSON>n", "<PERSON><PERSON> giao hàng", "<PERSON><PERSON> hoàn thành", "<PERSON><PERSON> hủy"]}, "paymentStatus": {"type": "boolean"}, "user": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "DonHangListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DonHang"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "DonHang": {"type": "object", "required": ["code", "priceAfterTax", "taxAmount", "discountAmount", "shippingAmount", "statusOrder", "paymentStatus"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "priceAfterTax": {"type": "integer"}, "taxAmount": {"type": "integer"}, "discountAmount": {"type": "integer"}, "shippingAmount": {"type": "integer"}, "orderItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "productName": {"type": "string"}, "productPrice": {"type": "integer"}, "quantity": {"type": "integer"}, "totalPrice": {"type": "integer"}, "productImage": {"type": "string"}, "order": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "code": {"type": "string"}, "priceAfterTax": {"type": "integer"}, "taxAmount": {"type": "integer"}, "discountAmount": {"type": "integer"}, "shippingAmount": {"type": "integer"}, "orderItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "statusOrder": {"type": "string", "enum": ["<PERSON>ờ x<PERSON>c n<PERSON>n", "<PERSON><PERSON> giao hàng", "<PERSON><PERSON> hoàn thành", "<PERSON><PERSON> hủy"]}, "paymentStatus": {"type": "boolean"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "provider": {"type": "string"}, "resetPasswordToken": {"type": "string"}, "confirmationToken": {"type": "string"}, "confirmed": {"type": "boolean"}, "blocked": {"type": "boolean"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "name": {"type": "string"}, "phone": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "product": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "price": {"type": "integer"}, "discount": {"type": "integer"}, "category": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "pathId": {"type": "integer"}, "parent": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "path": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "hot": {"type": "boolean"}, "image": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "statusOrder": {"type": "string", "enum": ["<PERSON>ờ x<PERSON>c n<PERSON>n", "<PERSON><PERSON> giao hàng", "<PERSON><PERSON> hoàn thành", "<PERSON><PERSON> hủy"]}, "paymentStatus": {"type": "boolean"}, "user": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "DonHangResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/DonHang"}, "meta": {"type": "object"}}}, "SanPhamRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["name", "price", "discount", "hot", "image"], "type": "object", "properties": {"name": {"type": "string"}, "price": {"type": "integer"}, "discount": {"type": "integer"}, "category": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "hot": {"type": "boolean"}, "image": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}, "description": {"type": "string"}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "SanPhamListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/San<PERSON>ham"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "SanPham": {"type": "object", "required": ["name", "price", "discount", "hot", "image"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "price": {"type": "integer"}, "discount": {"type": "integer"}, "category": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "image": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "pathId": {"type": "integer"}, "parent": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "path": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "hot": {"type": "boolean"}, "image": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "price": {"type": "integer"}, "discount": {"type": "integer"}, "category": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "hot": {"type": "boolean"}, "image": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "SanPhamResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/San<PERSON>ham"}, "meta": {"type": "object"}}}, "ThietLapGiaoDienRequest": {"type": "object", "required": ["data"], "properties": {"data": {"required": ["color", "logo", "zaloOA", "shippingAmount"], "type": "object", "properties": {"color": {"type": "string"}, "logo": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}, "zaloOA": {"type": "string"}, "shippingAmount": {"type": "integer"}, "paymentMethod": {}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"oneOf": [{"type": "integer"}, {"type": "string"}], "example": "string or id"}}}}}}, "ThietLapGiaoDienListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/ThietLapGiaoDien"}}, "meta": {"type": "object", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer", "minimum": 25}, "pageCount": {"type": "integer", "maximum": 1}, "total": {"type": "integer"}}}}}}}, "ThietLapGiaoDien": {"type": "object", "required": ["color", "logo", "zaloOA", "shippingAmount"], "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "color": {"type": "string"}, "logo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "pathId": {"type": "integer"}, "parent": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "children": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "files": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "resetPasswordToken": {"type": "string"}, "registrationToken": {"type": "string"}, "isActive": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "description": {"type": "string"}, "users": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "action": {"type": "string"}, "actionParameters": {}, "subject": {"type": "string"}, "properties": {}, "conditions": {}, "role": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "blocked": {"type": "boolean"}, "preferedLanguage": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}, "path": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "zaloOA": {"type": "string"}, "shippingAmount": {"type": "integer"}, "paymentMethod": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "color": {"type": "string"}, "logo": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "formats": {}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "float"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {}, "related": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}, "folder": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "folderPath": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}, "zaloOA": {"type": "string"}, "shippingAmount": {"type": "integer"}, "paymentMethod": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "updatedBy": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}, "locale": {"type": "string"}, "localizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "documentId": {"type": "string"}}}}}}}}}, "ThietLapGiaoDienResponse": {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/ThietLapGiaoDien"}, "meta": {"type": "object"}}}, "UploadFile": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}, "alternativeText": {"type": "string"}, "caption": {"type": "string"}, "width": {"type": "number", "format": "integer"}, "height": {"type": "number", "format": "integer"}, "formats": {"type": "number"}, "hash": {"type": "string"}, "ext": {"type": "string"}, "mime": {"type": "string"}, "size": {"type": "number", "format": "double"}, "url": {"type": "string"}, "previewUrl": {"type": "string"}, "provider": {"type": "string"}, "provider_metadata": {"type": "object"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Users-Permissions-Role": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Users-Permissions-User": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "foo.bar"}, "email": {"type": "string", "example": "<EMAIL>"}, "provider": {"type": "string", "example": "local"}, "confirmed": {"type": "boolean", "example": true}, "blocked": {"type": "boolean", "example": false}, "createdAt": {"type": "string", "format": "date-time", "example": "2022-06-02T08:32:06.258Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2022-06-02T08:32:06.267Z"}}}, "Users-Permissions-UserRegistration": {"type": "object", "properties": {"jwt": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}, "user": {"$ref": "#/components/schemas/Users-Permissions-User"}}}, "Users-Permissions-PermissionsTree": {"type": "object", "additionalProperties": {"type": "object", "description": "every api", "properties": {"controllers": {"description": "every controller of the api", "type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"description": "every action of every controller", "type": "object", "properties": {"enabled": {"type": "boolean"}, "policy": {"type": "string"}}}}}}}}}, "requestBodies": {"Users-Permissions-RoleRequest": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "permissions": {"$ref": "#/components/schemas/Users-Permissions-PermissionsTree"}}}, "example": {"name": "foo", "description": "role foo", "permissions": {"api::content-type.content-type": {"controllers": {"controllerA": {"find": {"enabled": true}}}}}}}}}}}, "tags": [{"name": "Users-Permissions - Auth", "description": "Authentication endpoints", "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}, {"name": "Users-Permissions - Users & Roles", "description": "Users, roles, and permissions endpoints", "externalDocs": {"description": "Find out more", "url": "https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]}