/* OrderManagement specific styles */
.order-management {
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Antd Table customizations for OrderManagement */
.order-management .ant-table {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-table-thead > tr > th {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  color: #1e293b;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f1f5f9;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-table-tbody > tr:hover > td {
  background: #f8fafc;
}

/* Pagination styles */
.order-management .ant-pagination {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-pagination-item {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.order-management .ant-pagination-item-active {
  background: #2563eb;
  border-color: #2563eb;
}

.order-management .ant-pagination-item-active a {
  color: #ffffff;
}

/* Modal styles */
.order-management .ant-modal-header {
  border-bottom: 1px solid #e2e8f0;
}

.order-management .ant-modal-title {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
  color: #1e293b;
}

.order-management .ant-modal-body {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Tag styles */
.order-management .ant-tag {
  font-family: 'Be Vietnam Pro', sans-serif;
  border-radius: 6px;
  font-weight: 500;
}

/* Button styles */
.order-management .ant-btn {
  font-family: 'Be Vietnam Pro', sans-serif;
  border-radius: 6px;
  font-weight: 500;
}

/* Input styles */
.order-management .ant-input {
  font-family: 'Be Vietnam Pro', sans-serif;
  border-radius: 6px;
}

.order-management .ant-select {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-select-selector {
  border-radius: 6px;
}

/* DatePicker styles */
.order-management .ant-picker {
  font-family: 'Be Vietnam Pro', sans-serif;
  border-radius: 6px;
}

/* Checkbox styles */
.order-management .ant-checkbox-wrapper {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Radio styles */
.order-management .ant-radio-wrapper {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Tabs styles */
.order-management .ant-tabs {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-tabs-tab {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
}

/* Empty state styles */
.order-management .ant-empty {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-empty-description {
  color: #64748b;
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Spin styles */
.order-management .ant-spin {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-spin-text {
  font-family: 'Be Vietnam Pro', sans-serif;
  color: #64748b;
}

/* Badge styles */
.order-management .ant-badge {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Tooltip styles */
.order-management .ant-tooltip {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Space styles */
.order-management .ant-space {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Row and Col styles */
.order-management .ant-row {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-col {
  font-family: 'Be Vietnam Pro', sans-serif;
}

/* Form styles */
.order-management .ant-form {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-form-item-label > label {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 500;
  color: #1e293b;
}

/* Popconfirm styles */
.order-management .ant-popover {
  font-family: 'Be Vietnam Pro', sans-serif;
}

.order-management .ant-popover-title {
  font-family: 'Be Vietnam Pro', sans-serif;
  font-weight: 600;
  color: #1e293b;
}

.order-management .ant-popover-inner-content {
  font-family: 'Be Vietnam Pro', sans-serif;
  color: #64748b;
}

/* Custom scrollbar for better UX */
.order-management ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.order-management ::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.order-management ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.order-management ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .order-management .ant-table {
    font-size: 12px;
  }
  
  .order-management .ant-table-thead > tr > th {
    padding: 8px 4px;
  }
  
  .order-management .ant-table-tbody > tr > td {
    padding: 8px 4px;
  }
}

/* Loading state improvements */
.order-management .ant-spin-container {
  position: relative;
}

.order-management .ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* Focus states for accessibility */
.order-management .ant-btn:focus,
.order-management .ant-input:focus,
.order-management .ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}
