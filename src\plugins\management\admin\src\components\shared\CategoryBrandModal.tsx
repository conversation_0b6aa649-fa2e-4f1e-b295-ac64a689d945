import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Upload, message, Space, Button, Switch } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useFetchClient } from '@strapi/strapi/admin';
import type { UploadFile } from 'antd/es/upload/interface';

interface CategoryBrandModalProps {
  visible: boolean;
  onCancel: () => void;
  type: 'category' | 'brand' | 'news-category';
  editingItem?: any; // Item being edited (null for create mode)
  onSuccess?: () => void;
  showActiveSwitch?: boolean;
  autoActive?: boolean;
  title?: string;
}

const CategoryBrandModal: React.FC<CategoryBrandModalProps> = ({
  visible,
  onCancel,
  type,
  editingItem,
  onSuccess,
  showActiveSwitch = true,
  autoActive = true,
  title,
}) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const { post, put } = useFetchClient();

  const isEditMode = !!editingItem;

  // Set form values when editing
  useEffect(() => {
    if (editingItem && visible) {
      const formValues: any = {
        name: editingItem.name,
        isActive: editingItem.isActive,
      };

      if (type === 'brand') {
        formValues.description = editingItem.description;
        formValues.website = editingItem.website;
      }

      if (type === 'news-category') {
        formValues.description = editingItem.description;
      }

      form.setFieldsValue(formValues);

      // Set file list for existing image/logo
      const existingFile = type === 'brand' ? editingItem.logo : editingItem.image;
      if (existingFile) {
        setFileList([
          {
            uid: '-1',
            name: existingFile.name,
            status: 'done',
            url: existingFile.url,
          },
        ]);
      } else {
        setFileList([]);
      }
    } else if (!editingItem) {
      // Reset for create mode
      form.resetFields();
      setFileList([]);
    }
  }, [editingItem, visible, form, type]);

  const handleSubmit = async (values: any) => {
    if (submitting) return;

    setSubmitting(true);
    try {
      // Step 1: Upload file to media library first (if exists)
      let mediaId = null;
      if (fileList.length > 0 && fileList[0].originFileObj) {
        const uploadFormData = new FormData();
        uploadFormData.append('files', fileList[0].originFileObj);

        try {
          const uploadResponse = await post('/upload', uploadFormData);
          if (uploadResponse.data && uploadResponse.data.length > 0) {
            mediaId = uploadResponse.data[0].id;
          }
        } catch (uploadError) {
          console.error('Error uploading file:', uploadError);
          message.error('Không thể tải lên hình ảnh');
          setSubmitting(false);
          return;
        }
      }

      // Step 2: Create/update category/brand with media ID
      const isActiveValue = showActiveSwitch
        ? values.isActive !== undefined
          ? values.isActive
          : true
        : autoActive;

      const requestData: any = {
        name: values.name,
        isActive: isActiveValue,
      };

      // Add media reference
      if (mediaId) {
        // New image uploaded
        requestData[type === 'brand' ? 'logo' : 'image'] = mediaId;
      } else if (isEditMode) {
        // Edit mode: check if user removed existing image
        const hadImageBefore =
          editingItem && (type === 'brand' ? editingItem.logo : editingItem.image);
        const hasImageNow = fileList.length > 0;

        if (hadImageBefore && !hasImageNow) {
          // User removed the image - set to null
          requestData[type === 'brand' ? 'logo' : 'image'] = null;
        }
        // If hadImageBefore && hasImageNow (existing image kept), don't include field to preserve
        // If !hadImageBefore && !hasImageNow (no image before, no image now), don't include field
      } else {
        // Create mode: set to null if no image
        requestData[type === 'brand' ? 'logo' : 'image'] = null;
      }

      // Add brand-specific fields
      if (type === 'brand') {
        requestData.description = values.description || '';
        requestData.website = values.website || '';
      }

      // Add news-category-specific fields
      if (type === 'news-category') {
        requestData.description = values.description || '';
      }

      const endpoint =
        type === 'category'
          ? '/management/products/categories'
          : type === 'brand'
            ? '/management/products/brands'
            : '/management/news/categories';

      if (isEditMode) {
        await put(`${endpoint}/${editingItem.id}`, { data: requestData });
        const itemType =
          type === 'category' ? 'danh mục' : type === 'brand' ? 'thương hiệu' : 'danh mục tin tức';
        message.success(`Cập nhật ${itemType} thành công`);
      } else {
        await post(endpoint, { data: requestData });
        const itemType =
          type === 'category' ? 'danh mục' : type === 'brand' ? 'thương hiệu' : 'danh mục tin tức';
        message.success(`Tạo ${itemType} thành công`);
      }

      handleCancel();
      onSuccess?.();
    } catch (error: any) {
      console.error('Error saving:', error);
      const itemType =
        type === 'category' ? 'danh mục' : type === 'brand' ? 'thương hiệu' : 'danh mục tin tức';
      message.error(error.response?.data?.message || `Có lỗi xảy ra khi lưu ${itemType}`);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setFileList([]);
    onCancel();
  };

  const getModalTitle = () => {
    if (title) return title;

    if (isEditMode) {
      return type === 'category'
        ? 'Sửa danh mục'
        : type === 'brand'
          ? 'Sửa thương hiệu'
          : 'Sửa danh mục tin tức';
    } else {
      return type === 'category'
        ? 'Thêm danh mục mới'
        : type === 'brand'
          ? 'Thêm thương hiệu mới'
          : 'Thêm danh mục tin tức mới';
    }
  };

  return (
    <Modal title={getModalTitle()} open={visible} onCancel={handleCancel} footer={null} width={600}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ marginTop: 24 }}
        initialValues={{ isActive: true }}
      >
        <Form.Item
          label={type === 'brand' ? 'Tên thương hiệu' : 'Tên danh mục'}
          name="name"
          rules={[
            {
              required: true,
              message: `Vui lòng nhập tên ${type === 'brand' ? 'thương hiệu' : 'danh mục'}`,
            },
            {
              min: 2,
              message: `Tên ${type === 'brand' ? 'thương hiệu' : 'danh mục'} phải có ít nhất 2 ký tự`,
            },
          ]}
        >
          <Input
            placeholder={`Nhập tên ${type === 'brand' ? 'thương hiệu' : 'danh mục'}`}
            size="large"
          />
        </Form.Item>

        {type === 'brand' && (
          <>
            <Form.Item label="Mô tả" name="description">
              <Input.TextArea placeholder="Nhập mô tả thương hiệu" rows={3} size="large" />
            </Form.Item>

            <Form.Item label="Website" name="website">
              <Input placeholder="https://example.com" size="large" type="url" />
            </Form.Item>
          </>
        )}

        {type === 'news-category' && (
          <Form.Item label="Mô tả" name="description">
            <Input.TextArea placeholder="Nhập mô tả danh mục tin tức" rows={3} size="large" />
          </Form.Item>
        )}

        <Form.Item
          label={type === 'brand' ? 'Logo' : 'Hình ảnh'}
          name={type === 'brand' ? 'logo' : 'image'}
        >
          <Upload
            listType="picture-card"
            fileList={fileList}
            onChange={({ fileList: newFileList }) => setFileList(newFileList)}
            beforeUpload={() => false}
            maxCount={1}
          >
            {fileList.length < 1 && (
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Tải lên</div>
              </div>
            )}
          </Upload>
        </Form.Item>

        {showActiveSwitch && (
          <Form.Item label="Trạng thái" name="isActive" valuePropName="checked">
            <Switch checkedChildren="Hoạt động" unCheckedChildren="Tạm dừng" />
          </Form.Item>
        )}

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel} disabled={submitting}>
              Hủy
            </Button>
            <Button type="primary" htmlType="submit" loading={submitting} disabled={submitting}>
              {isEditMode ? 'Cập nhật' : type === 'brand' ? 'Tạo thương hiệu' : 'Tạo danh mục'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CategoryBrandModal;
