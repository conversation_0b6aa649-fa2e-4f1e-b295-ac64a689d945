const routes = [
  // Dashboard routes
  {
    method: 'GET',
    path: '/dashboard',
    handler: 'dashboard-controller.getDashboardData',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/kpi',
    handler: 'dashboard-controller.getKPIData',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/charts/:chartType',
    handler: 'dashboard-controller.getChartData',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/top-customers',
    handler: 'dashboard-controller.getTopAffiliates',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/dashboard/best-selling-products',
    handler: 'dashboard-controller.getBestSellingProducts',
    config: {
      policies: [],
    },
  },

  // Order routes
  {
    method: 'GET',
    path: '/orders',
    handler: 'order-controller.listOrders',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/orders',
    handler: 'order-controller.createOrder',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/orders/:id',
    handler: 'order-controller.getOrderDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/orders/:id/status',
    handler: 'order-controller.updateOrderStatus',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/orders/:id/customer',
    handler: 'order-controller.updateOrderCustomer',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/orders/export',
    handler: 'order-controller.exportOrdersToExcel',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/orders/:id/commission',
    handler: 'order-controller.updateCommission',
    config: {
      policies: [],
    },
  },

  // Commission routes
  {
    method: 'GET',
    path: '/commissions',
    handler: 'commission-controller.listCommissions',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/commissions/statistics',
    handler: 'commission-controller.getCommissionStatistics',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/commissions/:id',
    handler: 'commission-controller.updateCommission',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/commissions/:id/audit-logs',
    handler: 'commission-controller.getCommissionAuditLogs',
    config: {
      policies: [],
    },
  },

  // User routes
  {
    method: 'GET',
    path: '/users',
    handler: 'user-controller.listUsers',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/users/statistics',
    handler: 'user-controller.getUserStatistics',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/users/roles',
    handler: 'user-controller.getUserRoles',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/users',
    handler: 'user-controller.createUser',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/users/:id',
    handler: 'user-controller.getUserDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/users/:id/status',
    handler: 'user-controller.updateUserStatus',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/users/:id',
    handler: 'user-controller.updateUserInfo',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/users/export',
    handler: 'user-controller.exportUsersToExcel',
    config: {
      policies: [],
    },
  },

  // Withdrawal routes
  {
    method: 'GET',
    path: '/withdrawals',
    handler: 'withdrawal-controller.listWithdrawals',
    config: {
      policies: [],
    },
  },

  // News routes
  {
    method: 'GET',
    path: '/news/categories',
    handler: 'news-controller.getNewsCategories',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/news/categories',
    handler: 'news-controller.createNewsCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/news/categories/:id',
    handler: 'news-controller.updateNewsCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/news/categories/:id',
    handler: 'news-controller.deleteNewsCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/news/articles',
    handler: 'news-controller.getNewsArticles',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/news/articles/:id',
    handler: 'news-controller.getNewsArticleDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/news/articles',
    handler: 'news-controller.createNewsArticle',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/news/articles/:id',
    handler: 'news-controller.updateNewsArticle',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/news/articles/:id',
    handler: 'news-controller.deleteNewsArticle',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/withdrawals/:id',
    handler: 'withdrawal-controller.getWithdrawalDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/withdrawals/:id/approve',
    handler: 'withdrawal-controller.approveWithdrawal',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/withdrawals/:id/reject',
    handler: 'withdrawal-controller.rejectWithdrawal',
    config: {
      policies: [],
    },
  },

  // Product routes - specific routes first, then dynamic routes
  {
    method: 'GET',
    path: '/products',
    handler: 'product-controller.listProducts',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/categories',
    handler: 'product-controller.getProductCategories',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/brands',
    handler: 'product-controller.getProductBrands',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/products/categories',
    handler: 'product-controller.createProductCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/products/categories/:id',
    handler: 'product-controller.updateProductCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/products/categories/:id',
    handler: 'product-controller.deleteProductCategory',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/products/brands',
    handler: 'product-controller.createProductBrand',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/products/brands/:id',
    handler: 'product-controller.updateProductBrand',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/products/brands/:id',
    handler: 'product-controller.deleteProductBrand',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/categories/debug',
    handler: 'product-controller.debugCategories',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/categories/check-schema',
    handler: 'product-controller.checkCategorySchema',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/export',
    handler: 'product-controller.exportProducts',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/products/import',
    handler: 'product-controller.importProducts',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/sample',
    handler: 'product-controller.downloadSampleFile',
    config: {
      policies: [],
      auth: false,
    },
  },
  {
    method: 'POST',
    path: '/products/seed-data',
    handler: 'product-controller.seedCategoriesAndBrands',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/products/bulk-update',
    handler: 'product-controller.bulkUpdateProducts',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/products/:id',
    handler: 'product-controller.getProductDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/products',
    handler: 'product-controller.createProduct',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/products/:id',
    handler: 'product-controller.updateProduct',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/products/:id',
    handler: 'product-controller.deleteProduct',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/products/:id/status',
    handler: 'product-controller.updateProductStatus',
    config: {
      policies: [],
    },
  },

  // Promotion routes
  {
    method: 'GET',
    path: '/promotions',
    handler: 'promotion-controller.listPromotions',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/promotions',
    handler: 'promotion-controller.createPromotion',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/promotions/statistics',
    handler: 'promotion-controller.getPromotionStatistics',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/promotions/:id',
    handler: 'promotion-controller.getPromotionDetail',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/promotions/:id',
    handler: 'promotion-controller.updatePromotion',
    config: {
      policies: [],
    },
  },
  {
    method: 'DELETE',
    path: '/promotions/:id',
    handler: 'promotion-controller.deletePromotion',
    config: {
      policies: [],
    },
  },
  {
    method: 'PUT',
    path: '/promotions/:id/status',
    handler: 'promotion-controller.togglePromotionStatus',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/promotions/:id/usage-history',
    handler: 'promotion-controller.getPromotionUsageHistory',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/promotions/validate/:code',
    handler: 'promotion-controller.validatePromotionCode',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/promotions/apply',
    handler: 'promotion-controller.applyPromotion',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/promotions/seed',
    handler: 'promotion-controller.seedPromotions',
    config: {
      policies: [],
    },
  },
];

export default routes;
