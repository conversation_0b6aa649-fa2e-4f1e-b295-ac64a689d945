import React from 'react';
import { <PERSON>, Edit, Trash2, Loader2 } from 'lucide-react';
import { Popconfirm, Tooltip } from 'antd';
import { ActionButtons, ActionButton } from './StyledComponents';

interface ActionButtonGroupProps {
  onView?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  deleteConfirmTitle?: string;
  deleteConfirmDescription?: string;
  showView?: boolean;
  showEdit?: boolean;
  showDelete?: boolean;
  viewTooltip?: string;
  editTooltip?: string;
  deleteTooltip?: string;
  viewLoading?: boolean;
  editLoading?: boolean;
  deleteLoading?: boolean;
  disabled?: boolean;
  customActions?: React.ReactNode;
}

const ActionButtonGroup: React.FC<ActionButtonGroupProps> = ({
  onView,
  onEdit,
  onDelete,
  deleteConfirmTitle = 'Xác nhận xóa',
  deleteConfirmDescription = 'Bạn có chắc chắn muốn xóa mục này?',
  showView = false,
  showEdit = true,
  showDelete = true,
  viewTooltip = 'Xem chi tiết',
  editTooltip = 'Chỉnh sửa',
  deleteTooltip = 'Xóa',
  viewLoading = false,
  editLoading = false,
  deleteLoading = false,
  disabled = false,
  customActions,
}) => {
  return (
    <ActionButtons>
      {showView && onView && (
        <Tooltip title={viewTooltip}>
          <ActionButton $variant="view" onClick={onView} disabled={disabled || viewLoading}>
            {viewLoading ? <Loader2 className="animate-spin" /> : <Eye />}
          </ActionButton>
        </Tooltip>
      )}

      {showEdit && onEdit && (
        <Tooltip title={editTooltip}>
          <ActionButton $variant="edit" onClick={onEdit} disabled={disabled || editLoading}>
            {editLoading ? <Loader2 className="animate-spin" /> : <Edit />}
          </ActionButton>
        </Tooltip>
      )}

      {showDelete && onDelete && (
        <Popconfirm
          title={deleteConfirmTitle}
          description={deleteConfirmDescription}
          onConfirm={onDelete}
          okText="Xóa"
          cancelText="Hủy"
          okType="danger"
          disabled={disabled || deleteLoading}
        >
          <Tooltip title={deleteTooltip}>
            <ActionButton $variant="delete" disabled={disabled || deleteLoading}>
              {deleteLoading ? <Loader2 className="animate-spin" /> : <Trash2 />}
            </ActionButton>
          </Tooltip>
        </Popconfirm>
      )}

      {customActions}
    </ActionButtons>
  );
};

export default ActionButtonGroup;
