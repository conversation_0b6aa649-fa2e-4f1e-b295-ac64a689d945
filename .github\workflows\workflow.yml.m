# name: Deploy oferify-strapi

# on:
#   push:
#     branches:
#       - 'main'
#       # - 'ci'

# jobs:
#   build:
#     runs-on: ubuntu-latest
#     env:
#       NODE_OPTIONS: '--max_old_space_size=4096'
#       URL: https://admin.oferify.com
#     strategy:
#       matrix:
#         node-version: [14.x]
#     steps:
#       - uses: actions/checkout@v1
#       - run: yarn
#       - run: yarn build
#       - run: zip -r build.zip build

#       - name: copy file via ssh password
#         uses: appleboy/scp-action@master
#         with:
#           host: ${{ secrets.HOST }}
#           username: ${{ secrets.USERNAME }}
#           password: ${{ secrets.PASSWORD }}
#           source: "build.zip"
#           target: "/root/oferify/oferify-strapi"

#       - name: executing remote ssh commands using password
#         uses: appleboy/ssh-action@master
#         with:
#           host: ${{ secrets.HOST }}
#           username: ${{ secrets.USERNAME }}
#           password: ${{ secrets.PASSWORD }}
#           script: |
#             cd /root/oferify/oferify-strapi
#             eval `ssh-agent -s`
#             ssh-add ~/.ssh/huylv-lev
#             git pull origin main
#             git status
#             yarn
#             rm -rf build
#             unzip build.zip
#             pm2 restart oferify-strapi

#       - name: Send mail
#         uses: dawidd6/action-send-mail@v3
#         with:
#           connection_url: ${{secrets.MAIL_CONNECTION}}
#           server_address: smtp.gmail.com
#           server_port: 465
#           secure: true
#           subject: 'Deploy successfully: ${{github.repository}}: ${{ github.event.head_commit.message }}'
#           to: <EMAIL>
#           from: GithubActionBot
#           body: Build job of ${{github.repository}} completed successfully!
#           ignore_cert: true
#           convert_markdown: true
#           priority: low
