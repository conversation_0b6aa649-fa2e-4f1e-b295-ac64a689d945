import type { StrapiApp } from "@strapi/strapi/admin";
import vi from "./extensions/translations/vi.json";

export default {
  config: {
    locales: ["vi"],
    translations: {
      vi,
    },
    notifications: {
      releases: false,
    },
    defaultLocale: "vi",
    theme: {
      light: {
        colors: {
          primary100: "#E6F3FF",
          primary200: "#B3D9FF",
          primary500: "#0066CC",
          buttonPrimary500: "#0066CC",
          primary600: "#0052A3",
          buttonPrimary600: "#0052A3",
          primary700: "#003D7A",
          primary800: "#002952",
          primary900: "#001429",
          alternative100: "#F0F9FF",
          alternative200: "#E0F2FE",
          alternative500: "#0EA5E9",
          alternative600: "#0284C7",
          alternative700: "#0369A1",
          buttonSecondary500: "#6B7280",
          buttonSecondary600: "#4B5563",
          danger100: "#FEF2F2",
          danger200: "#FEE2E2",
          danger500: "#EF4444",
          danger600: "#DC2626",
          danger700: "#B91C1C",
          neutral0: "#FFFFFF",
          neutral100: "#F9FAFB",
          neutral150: "#F3F4F6",
          neutral200: "#E5E7EB",
          neutral300: "#D1D5DB",
          neutral400: "#9CA3AF",
          neutral500: "#6B7280",
          neutral600: "#4B5563",
          neutral700: "#374151",
          neutral800: "#1F2937",
          neutral900: "#111827",
          success100: "#ECFDF5",
          success200: "#D1FAE5",
          success500: "#10B981",
          success600: "#059669",
          success700: "#047857",
          warning100: "#FFFBEB",
          warning200: "#FEF3C7",
          warning500: "#F59E0B",
          warning600: "#D97706",
          warning700: "#B45309",
        },
      },
    },
  },
  bootstrap(app: StrapiApp) {
    document.title = "Libertyholdings";
  },
};
