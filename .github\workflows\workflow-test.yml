name: Deploy stbe STRAPI TEST

on:
  push:
    branches:
      - 'test'
      - 'test_*'
      - 'dev'
      - 'dev_*'
env:
  REPO: stbe

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: '--max_old_space_size=4096'
      URL: https://test-stbe.ofersoft.vn
    strategy:
      matrix:
        node-version: [14.x]
    steps:
      - uses: actions/checkout@v1
      - run: yarn install-lib
      - run: yarn build
      - run: zip -r build.zip build

      - name: copy file via ssh password
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST_TEST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD_TEST }}
          source: "build.zip"
          target: "/root/${{ env.REPO }}/${{ env.REPO }}-strapi"

      - name: executing remote ssh commands using password
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_TEST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD_TEST }}
          script: |
            cd /root/${{ env.REPO }}/${{ env.REPO }}-strapi
            git reset --h
            git checkout ${{ github.ref_name }}
            git pull
            git reset --h ${{ github.ref_name }}
            git pull
            git status
            yarn
            unzip build.zip -d build2
            mv build build3
            mv build2/build build
            rm -rf build2 build3
            pm2 restart ${{ env.REPO }}-strapi
